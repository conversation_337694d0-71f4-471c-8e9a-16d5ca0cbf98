<?php
// Load composer namespacing
require_once __DIR__ . '/vendor/autoload.php';

// Load theme files
import_folder( __DIR__ . '/inc' );
import_folder( __DIR__ . '/commands' );

// Non-environnement Constants
define( 'DEFAULT_IMAGE', 'https://source.unsplash.com/random' );

// Allow media files extensions
function add_file_types_to_uploads($mimes) {
	$mimes['svg'] = 'image/svg+xml';
	$mimes['epub'] = 'application/epub+zip';
	return $mimes;
}

/**
 * Hook actions
 * functions are imported from files in /inc
 * default values are pririty 10 and args 1
 */
$hooks_actions = [
	'init' => [
		25 => ['name' => 'load_theme_translation', 'args' => 1],
		20 => ['name' => 'kryzaplate_register_patterns', 'args' => 1],
		15 => ['name' => 'register_post_types', 'args' => 1],
		10 => ['name' => 'register_taxonomies', 'args' => 1],
		5 => ['name' => 'remove_default_tags', 'args' => 1],
	],
	'after_setup_theme' => [
		10 => ['name' => 'theme_supports', 'args' => 1],
	],
	'wp_enqueue_scripts' => [
		10 => ['name' => 'adding_scripts_and_styles', 'args' => 1],
	],
	'enqueue_block_editor_assets' => [
		15 => ['name' => 'add_custom_editor_style', 'args' => 1],
		10 => ['name' => 'be_gutenberg_scripts', 'args' => 1],
	],
	'timber/context' => [
		10 => ['name' => 'add_to_context', 'args' => 1],
	],
	'timber/twig' => [
		10 => ['name' => 'add_to_twig', 'args' => 1],
	],
	'acf/init' => [
		10 => ['name' => 'register_custom_blocks', 'args' => 1],
		20 => ['name' => 'add_acf_options_page', 'args' => 1],
	],
	'pre_get_posts' => [
		10 => ['name' => 'meta_or_title', 'args' => 1],
	],
	'phpmailer_init' => [
        10 => ['name' => 'mailtrap', 'args' => 1],
    ],
	'generate_rewrite_rules' => [
		10 => ['name' => 'add_rewrite_rules', 'args' => 1]
    ],
	'template_redirect' => [
		10 => ['name' => 'disable_author_archive', 'args' => 1]
    ],
	'login_errors' => [
		5 => ['name' => 'no_wordpress_errors', 'args' => 1],
	],
];

foreach ( $hooks_actions as $hook => $functions ) {
	foreach ($functions as $priority => $function) {
		add_action($hook, $function['name'], $priority, $function['args']);
	}
};

/**
 * Hook filters
 * functions are imported from files in $includes
 * default values are pririty 10 and args 1
 */
$hooks_filters = [
	'acf/validate_value/name=postal_code' => [
		10 => ['name' => 'validate_postal_code', 'args' => 4],
	],
	'acf/settings/show_admin' => [
		10 => ['name' => 'hide_acf_admin', 'args' => 1],
	],
	'acf/settings/save_json' => [
		10 => ['name' => 'acf_json_save', 'args' => 1],
	],
	'acf/settings/load_json' => [
		10 => ['name' => 'acf_json_load', 'args' => 1],
	],
	'use_block_editor_for_post_type' => [
		10 => ['name' => 'disable_gutenberg', 'args' => 1],
	],
	'allowed_block_types_all' => [
		10 => ['name' => 'gutenberg_allowed_block_types', 'args' => 1],
	],
	'upload_mimes' => [
		10 => ['name' => 'add_file_types_to_uploads', 'args' => 1],
	],
	'template_include' => [
		10 => ['name' => 'redirect_archive_template', 'args' => 1],
		20 => ['name' => 'get_password_protected_template', 'args' => 1],
	],
	'pre_get_posts' => [
		10 => ['name' => 'search_filter', 'args' => 1],
	],
	'locale' => [
		10 => ['name' => 'set_current_language', 'args' => 1],
	],
 	'post_type_archive_link' =>
	 	[10 => ['name' => 'change_nouvelle_archive_link', 'args' => 2]
	],
 	'register_post_type_args' =>
	 	[20 => ['name' => 'customize_default_wp_post_type', 'args' => 2]
	],
	'login_headerurl' => [
        10 => ['name' => 'change_login_url', 'args' => 1],
    ],
    'login_enqueue_scripts' => [
        10 => ['name' => 'add_custom_editor_style', 'args' => 1],
    ],
    'wpseo_sitemap_exclude_author' => [
        10 => ['name' => 'sitemap_exclude_authors', 'args' => 1],
    ],
    'rest_endpoints' => [
        10 => ['name' => 'hide_rest_author_api', 'args' => 1],
    ],
    'the_content' => [
        10 => ['name' => 'replace_image_urls', 'args' => 1],
    ],
	'wp_get_attachment_url' => [
        10 => ['name' => 'replace_image_urls', 'args' => 1],
    ],
	'post_thumbnail_html' => [
        10 => ['name' => 'replace_image_urls', 'args' => 1],
    ]
];

foreach ( $hooks_filters as $hook => $functions ) {
	foreach ($functions as $priority => $function) {
        add_filter($hook, $function['name'], $priority, $function['args']);
	}
}

/**
 * Commands
 * The condition is important, WP_CLI class does not exist in admin.
 */
if (class_exists( 'WP_CLI')) {
	WP_CLI::add_command('duplicate_post', 'DuplicatePost');
}

/**
 * Functions
 * Put here any function which use is very general. Please always try to keep this
 * file small and clean, and divide it into the /inc folder.
 */
function import_folder( string $path ) {
	$includes = scandir( $path );
	foreach ( $includes as $file ) {
		$path_file = $path . '/' . $file;
		// Require every file in path
		if ( ! is_dir( $path_file ) ) {
			require_once $path_file;
		} elseif ( $file !== '.' && $file !== '..' ) { // Import recursively files in directories that are not the directory itself nor the parent
			import_folder( $path_file );
		}
	}
}

/** 
 * Block User enumerations on user for prevent brute-force on passwords 
 * Sources: https://m0n.co/enum
 * **/
if (!is_admin()) {
	// default URL format
	if (preg_match('/author=([0-9]*)/i', $_SERVER['QUERY_STRING'])) die();
	add_filter('redirect_canonical', 'shapeSpace_check_enum', 10, 2);
}
function shapeSpace_check_enum($redirect, $request) {
	// permalink URL format
	if (preg_match('/\?author=([0-9]*)(\/*)/i', $request)) die();
	else return $redirect;
}

/**
 * redefined Wordpress local value if in ajax/fr url
 * @TODO: change this in the future
 */
function set_current_language($locale) {
	if (empty($_SERVER['REQUEST_URI'])) return;
	$split = explode('/', $_SERVER['REQUEST_URI']);
	if ($split[1] == 'ajax') {
		if ($split[2] == 'en') return 'en_US';
	}
	return $locale;
}

/** 
 * Send email to Mailtrap if Debug is set to True 
 * See 1Password for access 
 * */
function mailtrap($phpmailer) {
	if (WP_DEBUG_MAIL != true) return;

    $phpmailer->isSMTP();
    $phpmailer->Host = 'smtp.mailtrap.io';
    $phpmailer->SMTPAuth = true;
    $phpmailer->Port = 2525;
    $phpmailer->Username = 'c42372a24b3ca7';
    $phpmailer->Password = '3c0244a1dafdd6';
}


// Optional - Remove default post type
/*
function remove_default_post_type($args, $postType) {
    if ($postType === 'post') {
        $args['public']                = false;
        $args['show_ui']               = false;
        $args['show_in_menu']          = false;
        $args['show_in_admin_bar']     = false;
        $args['show_in_nav_menus']     = false;
        $args['can_export']            = false;
        $args['has_archive']           = false;
        $args['exclude_from_search']   = true;
        $args['publicly_queryable']    = false;
        $args['show_in_rest']          = false;
    }

    return $args;
}
*/
// add_filter('register_post_type_args', 'remove_default_post_type', 0, 2);


// AOS shortcut
// function aos ($delay = 100, $type = 'fade-up', $offset = null) {
//     return 'data-aos="' . $type . '" data-aos-delay="' . $delay . '"' . ($offset ? ' data-aos-offset="' . $offset . '"' : '');
// }

// Replace local url with staging for images
// if (defined('USE_REMOTE_IMAGES') and !!USE_REMOTE_IMAGES) {
//     add_filter( 'wp_get_attachment_image_src', function ($image) {
//         $image[0] = str_replace('http://enero.local', 'https://enero.kryzastage4.com', $image[0]);
//         return $image;
//     }, 10, 3 );
// }

// Hide ACF menu based on constant
// if (defined('HIDE_ACF_MENU') and !!HIDE_ACF_MENU) {
//     add_filter('acf/settings/show_admin', '__return_false');
// }

// Disable comments site-wide
// add_filter('comments_open', '__return_false', 20, 2);
// add_filter('pings_open', '__return_false', 20, 2);
// add_filter('comments_array', '__return_empty_array', 10, 2);
// add_action('admin_menu', function () { remove_menu_page('edit-comments.php'); });
// add_action('init', function () { if (is_admin_bar_showing()) remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60); });


// Fix a long-standing issue with ACF, where fields sometimes aren't shown
// https://support.advancedcustomfields.com/forums/topic/custom-fields-on-post-preview/#post-150273

// in previews (ie. from Preview > Open in new tab).
if ( isset( $_GET['preview']) && class_exists( 'acf_revisions' ) )
{
	// Reference to ACF's <code>acf_revisions</code> class
	// We need this to target its method, acf_revisions::acf_validate_post_id
	$acf_revs_cls = acf()->revisions;

	// This hook is added the ACF file: includes/revisions.php:36 (in ACF PRO v5.11)
	remove_filter( 'acf/validate_post_id', array( $acf_revs_cls, 'acf_validate_post_id', 10 ) );
}

// change icon picker path
function acf_svg_icon_picker_folder($path_suffix) {
    return 'assets/images/icons/';
}
add_filter('acf_svg_icon_picker_folder', 'acf_svg_icon_picker_folder');


/**
 * Create anchors for index in pages
 * 
 * Call thie functon before create post variable
 * because this function transforme $timber_post object
 *
 * @param  Timber\Post $timber_post
 * @return array $index_title key => value with id and title
 */
function createIndexAnchor(Timber\Post $timber_post)
{
	$post = get_post($timber_post->id);

	$blocks_list = parse_blocks($post->post_content);
	$index_title = [];
	processBlocks($blocks_list, $index_title);

	return $index_title;
}

/**
 * Parse recursively blocks to find heading with id
 * This function is used by createIndexAnchor
 * 
 * @param  array $blocks
 * @return array $index_title key => value with id and title
 */
function processBlocks($blocks, &$index_title) {
    foreach ($blocks as $block) {
        if ($block['blockName'] === "core/heading" && strpos($block['innerHTML'], 'id=') !== false) {
            $title = strip_tags($block['innerHTML']);
            $id = '';
            if (preg_match('/(.*)id="([a-zA-Z0-9\-\_]*)"/', $block['innerHTML'], $output_array)) {
                $id = $output_array[count($output_array) - 1];
            }
            $id = verifyIndex($index_title, $id);
            $index_title[$id] = $title;
        } elseif (!empty($block['innerBlocks'])) {
            processBlocks($block['innerBlocks'], $index_title);
        }
    }
}

/**
 * function for change key if multi title is similar
 * add number if already exist in array
 *
 * @param  array $index_title
 * @param  string $value
 * @param  int $key
 * @return string
 */
function verifyIndex(array $index_title, string $value, int $key = 0)
{
	$new_value = ($key == 0) ? $value : $value . '-' . $key;
	if (key_exists($new_value, $index_title)) {
		$key++;
		$new_value = verifyIndex($index_title, $value, $key);
	}
	return $new_value;
}
