<?php

$context = Timber::context();
$timber_post = Timber::get_post();

// Get the current post's industry terms
$current_industries = wp_get_post_terms($timber_post->ID, 'industry', array('fields' => 'ids'));

// Works query
$args = [
    'post_type'      => 'work',
    'post_status'    => 'publish',
    'posts_per_page' => 12,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_works = new Timber\PostQuery( $query );

// Expertises query
$args = [
    'post_type'      => 'expertise',
    'post_status'    => 'publish',
    'posts_per_page' => -1,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_expertises = new Timber\PostQuery( $query );


$context['post'] = $timber_post;
$context['related_works'] = $related_works;
$context['related_expertises'] = $related_expertises;

Timber::render( array( 'pages/singles/single-expertise.twig' ), $context );
