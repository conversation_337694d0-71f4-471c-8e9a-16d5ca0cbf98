<?php

$context = Timber::context();
$timber_post = Timber::get_post();

// Get the current post's industry terms
$current_industries = wp_get_post_terms($timber_post->ID, 'industry', array('fields' => 'ids'));

// Processes query
$args = [
    'post_type'      => 'process',
    'post_status'    => 'publish',
    'posts_per_page' => 12,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_processes = new Timber\PostQuery( $query );

// Works query
$args = [
    'post_type'      => 'work',
    'post_status'    => 'publish',
    'posts_per_page' => 12,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_works = new Timber\PostQuery( $query );

// Expertises query
$args = [
    'post_type'      => 'expertise',
    'post_status'    => 'publish',
    'posts_per_page' => -1,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_expertises = new Timber\PostQuery( $query );

// Post query
$args = [
    'post_type'      => 'post',
    'post_status'    => 'publish',
    'posts_per_page' => 3,
    'orderby'        => 'date',
    'order'          => 'DESC',
];

// Add industry taxonomy filter if current post has industry terms
if (!empty($current_industries)) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'industry',
            'field'    => 'term_id',
            'terms'    => $current_industries,
        ]
    ];
}

$query = new WP_Query($args);

$related_posts = new Timber\PostQuery( $query );

$context['post'] = $timber_post;
$context['related_processes'] = $related_processes;
$context['related_works'] = $related_works;
$context['related_expertises'] = $related_expertises;
$context['related_posts'] = $related_posts;

Timber::render( array( 'pages/singles/single-industry-sheet.twig' ), $context );
