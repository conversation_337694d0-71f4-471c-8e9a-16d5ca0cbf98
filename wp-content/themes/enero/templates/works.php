<?php
/**
 * Template Name: Réalisations
 */
$context = Timber::context();
$timber_post = Timber::get_post();

$limit    = empty($_GET['limit']) ? 20 : $_GET['limit'];
$paged    = empty($_GET['pg']) ? 1 : $_GET['pg'];

$industry = empty($_GET['industry']) ? '' : $_GET['industry'];

$args = [
    'post_type'      => 'work',
    'post_status'    => 'publish',
    'posts_per_page' => $limit,
    'paged'           => $paged,
    'orderby'        => 'title',
    'order'          => 'ASC',
];

$args['tax_query'] = [
    'relation' => 'AND'
];

if (!empty($industry)) {
    if ($industry !== 'all') {
        $industry = is_array($industry) ? $industry : explode(',', $industry);
        $args['tax_query'][] = [
            [
                'taxonomy' => 'industry',
                'field' => 'id',
                'terms' => $industry
            ]
        ];
    }
}

$query = new WP_Query($args);

$results = new Timber\PostQuery( $query );


// Define filters model
$context['filters'] = array(
    'filters' => array(
        array(
            'label' => __('Industrie', 'enero'),
            'id' => 'industry',
            'type' => 'select',
            'options' => array(),
            'skip' => false,
        )
    ),
);

// Feed filters from taxonomies

// Set category types
$category = get_terms([
    'taxonomy' => 'industry',
    'hide_empty' => false
]);
foreach ($category as $term) {
    $context['filters']['filters'][0]['options'][] = array(
        'label' => $term->name,
        'value' => $term->term_id,
    );
}


// Set all values
$context['params'] = $_GET;
$context['post'] = $timber_post;
$context['url'] = '/ajax/works-list';
$context['limit'] = $limit;
$context['results'] = $results;   


Timber::render( array( 'pages/archives/archives-works.twig' ), $context );
