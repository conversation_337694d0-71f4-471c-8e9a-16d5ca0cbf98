<?php

global $params;

$limit   = empty($params['limit']) ? 15 : $params['limit'];
$paged   = empty($params['pg']) ? 1 : $params['pg'];

$industry = empty($_GET['industry']) ? '' : $_GET['industry'];

$args = [
    'post_type'      => 'work',
    'post_status'    => 'publish',
    'posts_per_page' => $limit,
    'paged'           => $paged,
    'orderby'        => 'title',
    'order'          => 'ASC',
];

$args['tax_query'] = [
    'relation' => 'AND'
];

if (!empty($industry)) {
    if ($industry !== 'all') {
        $industry = is_array($industry) ? $industry : explode(',', $industry);
        $args['tax_query'][] = [
            [
                'taxonomy' => 'industry',
                'field' => 'id',
                'terms' => $industry
            ]
        ];
    }
}

$query = new WP_Query($args);

$results = new Timber\PostQuery( $query );


if ($results->found_posts > 0) {
    $response     = '';
    $context      = Timber::context();
    $response    .= Timber::compile('partials/lists/works-list.twig', [
        'results' => $results, 
        'theme' => $context['theme']
    ]);
    $data['html'] = $response;
}
else {
    $data['html'] = Timber::compile('partials/lists/no-result.twig');
}

wp_reset_query();
wp_reset_postdata();
return wp_send_json($data);