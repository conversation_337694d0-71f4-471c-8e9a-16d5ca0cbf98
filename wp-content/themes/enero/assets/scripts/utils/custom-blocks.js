import { Navigation } from 'swiper/modules';
import Swiper from 'swiper';

Swiper.use([Navigation]);

// Apply Swiper mechanics to all single-image sliders
document.querySelectorAll('.gutenberg-slider-single').forEach((el, index) => {
  const id = 'slider-' + index;
  el.setAttribute('id', id);
  const swiperContainer = el.querySelector('.swiper-container');
  const slider = new Swiper(swiperContainer, {
      speed: 300,
      loop: true,
      navigation: { nextEl: `#${id} .next`, prevEl: `#${id} .prev`, }
  });
});

// Apply Swiper mechanics to all multiple-image sliders
document.querySelectorAll('.gutenberg-slider-multiple').forEach((el, index) => {
  const id = 'slider-multiple-' + index;
  el.setAttribute('id', id);
  const swiperContainer = el.querySelector('.swiper-container');
  const slider = new Swiper(swiperContainer, {
      speed: 300,
      loop: false,
      slidesPerView: 1,
      spaceBetween: 30,
      navigation: { nextEl: `#${id} .next`, prevEl: `#${id} .prev` },
      breakpoints: {
        768: { slidesPerView: 3 },
        500: { slidesPerView: 2 },
      }
  });
});