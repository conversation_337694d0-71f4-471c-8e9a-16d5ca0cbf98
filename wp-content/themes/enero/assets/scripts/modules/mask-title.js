import { module } from 'modujs';
import { gsap } from "gsap";
import { SplitText } from "gsap/SplitText";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export default class extends module {

    constructor(m) {
        super(m);

        this.events = {
        }

        this.animatedTitle = this.el;
        
    }

    init() {

        gsap.registerPlugin(SplitText);

        const split = new SplitText(this.animatedTitle, {
            type: "words", 
        });

        split.words.forEach(wordEl => {
            const mask = document.createElement('span');
            mask.classList.add('word-mask');
            wordEl.parentNode.insertBefore(mask, wordEl);
            mask.appendChild(wordEl);
            wordEl.classList.add('word-content');

            gsap.set(wordEl, {
                yPercent: 110,
                opacity: 0,
            });
        });

        ScrollTrigger.create({
            trigger: this.animatedTitle, 
            start: "top 80%",
            once: true,
            onEnter: () => {
                this.animateWords();
            },
        });
    }

    animateWords() {
        const wordContents = this.animatedTitle.querySelectorAll('.word-content');

        gsap.to(wordContents, {
            yPercent: 0,
            opacity: 1,
            duration: 1,
            ease: 'power3.out',
            stagger: 0.08,
            delay: 0.3,
            onComplete: () => {
                this.animatedTitle.classList.add('-is-animated');
            }
        });
    }
    
    destroy() {
    }
}