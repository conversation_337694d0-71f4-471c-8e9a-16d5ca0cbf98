import { module } from 'modujs';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

/**
 * Absolute module
 *  <div class="grid"> 
 *      <main>
 *           WHATEVER
 *      </main>
 *      <aside>
 *           <div data-module-absolute>
 *               <div class="ctn">
 *                   YOUR STICKY CONTENT
 *               </div>
 *           </div>
 *      </aside>
 *  </div>
 */

export default class extends module {

    constructor(m) {
        super(m);

        this.$element = this.el.querySelector('.ctn');
        this.st = null;
        
        // Bind resize handler
        this.handleResize = this.handleResize.bind(this);
    }
    
    init() {
        this.createScrollTrigger();
        
        // Listen for window resize
        window.addEventListener('resize', this.handleResize);
    }
    
    createScrollTrigger() {
        // Only create ScrollTrigger for screens wider than 992px
        if (window.innerWidth > 992) {
            // Set Sticky animation on scroll 
            this.st = ScrollTrigger.create({
                trigger: this.el,
                start: "top 100px",
                endTrigger: this.el,
                end: () => `bottom ${this.$element.offsetHeight + 100}px`, // 100px is the top margin of the sticky element
                pin: true,
                pinSpacing: false,
                anticipatePin: 1,
                // markers: true,
            });
        }
    }
    
    handleResize() {
        // disable scrolltrigger if window width hits 992px or above
        if (window.innerWidth <= 992 && this.st) {
            this.st.kill();
            this.st = null;
        } else if (window.innerWidth > 992 && !this.st) {
            this.createScrollTrigger();
        }
    }

    // Method to destroy the instance (Modular hook)
    destroy() {
        if (this.st) {
            this.st.kill();
        }
        window.removeEventListener('resize', this.handleResize);
    }
}