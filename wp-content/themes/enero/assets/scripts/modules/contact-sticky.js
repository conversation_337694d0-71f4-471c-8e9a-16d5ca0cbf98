import { module } from 'modujs';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default class extends module {

    constructor(m) {
        super(m);
    }

    init() {
        const prefooter = document.querySelector('.pre-footer');
        
        // Set Sticky animation on scroll 
        this.st = ScrollTrigger.create({
            // trigger: this.$title,
            start: "bottom bottom-=80px",
            endTrigger: '#main-content',
            end: () => {
                const prefooterHeight = prefooter ? prefooter.offsetHeight : 0;
                console.log(prefooterHeight);
                return `bottom-=${prefooterHeight + 40}px bottom-=80px`;
            },
            pin: this.el,
            pinSpacing: false,
            anticipatePin: 1,
            // markers: true,
        });
    }

    destroy() {
    }
}