import { module } from 'modujs';
import { gsap } from "gsap";
import smooth from '../utils/scrollSmoother';
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default class extends module {

    constructor(m) {
        super(m);

        this.$links = this.$('link');
        this.scrollTriggerTimeout = null;
        this.scrollTriggersEnabled = true;

        this.events = {
            click: {
                link: 'scroll'
            }
        }
    }

    scroll(e) {
        e.preventDefault();
        const target = e.currentTarget.getAttribute('href');
        
        // Immediately set selected state on click
        this.$links.forEach(l => l.parentElement.classList.remove('-selected'));
        e.currentTarget.parentElement.classList.add('-selected');
        
        // Temporarily disable scroll trigger updates
        this.scrollTriggersEnabled = false;
        
        // Clear any existing timeout
        if (this.scrollTriggerTimeout) {
            clearTimeout(this.scrollTriggerTimeout);
        }
        
        // Re-enable scroll triggers after animation completes
        this.scrollTriggerTimeout = setTimeout(() => {
            this.scrollTriggersEnabled = true;
        }, 1000); // Adjust timing as needed
        
        smooth.scroll.scrollTo(target);
    }

    init() {
        // for each link create a scroll trigger that updates the selected state in anchors
        this.$links.forEach((link) => {
            this.st = ScrollTrigger.create({
                trigger: link.getAttribute('href'),
                start: "top top+=50",
                end: "bottom top+=50",
                onEnter: () => {
                    if (this.scrollTriggersEnabled) {
                        this.$links.forEach(l => l.parentElement.classList.remove('-selected'));
                        link.parentElement.classList.add('-selected');
                    }
                },
                onEnterBack: () => {
                    if (this.scrollTriggersEnabled) {
                        this.$links.forEach(l => l.parentElement.classList.remove('-selected'));
                        link.parentElement.classList.add('-selected');
                    }
                },
                // markers: true
            });
        });
    }
    
    destroy() {
    }
}