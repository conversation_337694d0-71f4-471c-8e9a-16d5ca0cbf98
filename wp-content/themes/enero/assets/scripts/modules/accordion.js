import { module } from 'modujs';
import { ScrollTrigger } from "gsap/ScrollTrigger";
import smooth from '../utils/scrollSmoother';

export default class extends module {

    constructor(m) {
        super(m);

        this.$accordions = this.$('accordion');
        this.allowMultiple = this.el.dataset.allowMultiple === 'true' ? true : false;

        this.events = {
            click: {
                button: 'toggleAccordion'
            }
        }
    }

    toggleAccordion(e) {

        const accordion = e.currentTarget.closest('.accordion-item');
        const content = accordion.querySelector('.accordion-content');
        const inner = accordion.querySelector('.inner');
        const height = inner.offsetHeight;
        const isOpen = accordion.classList.toggle('-open');

        if(!this.allowMultiple) {
            // Close all other accordions
            this.$accordions.forEach((accordion) => {
                if (accordion !== e.currentTarget.closest('.accordion-item')) {
                    accordion.classList.remove('-open');
                    accordion.querySelector('.accordion-content').style.height = '0px';
                }
            });
        }        

        accordion.setAttribute('aria-expanded', isOpen ? "true" : "false");
        content.style.height = isOpen ? height + 'px' : 0;

        setTimeout(() => {
            ScrollTrigger.refresh();
        }, 1000);
    }

    init() {

        this.$accordions.forEach((accordion) => {
            const inner = accordion.querySelector('.accordion-content');
            inner.style.height = '0px';
        });
    }
    
    destroy() {
    }
}