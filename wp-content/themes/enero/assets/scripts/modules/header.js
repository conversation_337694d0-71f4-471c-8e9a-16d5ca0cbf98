import { module } from 'modujs';
import * as focusTrap from 'focus-trap'; // ESM
import { ScrollTrigger } from "gsap/ScrollTrigger";
import smooth from '../utils/scrollSmoother'; 

export default class extends module {

    constructor(m) {
        super(m);

        this.tab;
        this.closeTimeout;
        this.mouseleaveHandler;
        this.mouseenterHandler;
        this.trap;

        this.$firstItem = this.$('first-item');
        this.$topLevelItem = document.querySelectorAll('.top-level');
        this.$sideMenu = this.$('side-menu')[0];
        this.$mobileMenuToggle = this.$('mobile-menu-toggle')[0];

        this.events = {
            click: {
                'side-menu': 'openSideMenu',
                'mobile-menu-toggle': 'toggleMobileMenu',
                'first-item': 'toggleTab'
            },
            mouseenter: {
                'first-item': 'toggleTab'
            },
            keyup: {
                'first-item': 'tabNav',
                'side-menu': 'tabNav'
            }
        }
    }

    toggleMobileMenu() {
        if (this.el.classList.contains('-open')) {
            this.el.classList.remove('-open');
            document.body.classList.remove('-no-scroll');
            this.$mobileMenuToggle.setAttribute('aria-expanded', 'false');
        } else {
            this.el.classList.add('-open');
            document.body.classList.add('-no-scroll');
            this.$mobileMenuToggle.setAttribute('aria-expanded', 'true');
        }
    }

    tabNav(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            
            // Check if this is a side menu element
            if (e.currentTarget === this.$sideMenu) {
                this.openSideMenu(e);
            } else {
                // For regular tabs, implement toggle functionality
                const target = e.currentTarget;
                const panel = target.querySelector('.header-panel');
                
                if (panel && panel.classList.contains('-is-open')) {
                    // If already open, close it
                    this.closeTab();
                } else {
                    // If closed, open it
                    this.openTab(e);
                }
            }
        }
    }

    openSideMenu(e) {
        const target = e.currentTarget;
        const panel = target.querySelector('.header-panel');
        const button = target.querySelector('button');

        this.resetAllTabs();

        // add class to the side menu (to its child)
        this.$sideMenu.classList.toggle('-toggled');

        if (panel && button) {
            const isOpen = panel.classList.toggle('-is-open');
            button.setAttribute('aria-expanded', isOpen.toString());
            panel.setAttribute('aria-hidden', (!isOpen).toString());
            this.el.classList.toggle('-has-tab-open');
            
            if (isOpen) {
                this.activateTrap(panel);
            } else {
                this.desActivateTrap();
            }
        }
    }

    toggleTab(e) {

        const deviceSize = window.innerWidth;
        const isClickEvent = e.type === 'click';

        // Cancel either click or hover event depending on device size
        if (deviceSize <= 1050 && isClickEvent) {
            // Close of open tab
            if (e.currentTarget.querySelector('.header-panel') === this.tab) {
                this.closeTab();
            } else {
                this.openTab(e);
            }
        }

        if (deviceSize > 1050 && !isClickEvent) {
            this.openTab(e);
        }

    }

    openTab(e) {
        const target = e.currentTarget;
        const button = target.querySelector('button');
        const panel = target.querySelector('.header-panel');

        this.$topLevelItem.forEach(item => {
            item.classList.toggle('-masked', item !== target);
        });

        if(panel){
            // Prevent weird cancel sometimes
            // Cancel any pending close timeout first
            this.cancelClose();
            // Remove event listeners from the current tab before switching
            if (this.tab) {
                this.removeTabEvents(this.tab);
            }
            
            this.resetAllTabs();
            this.tab = panel;
            
            if (panel && button) {
                // Set tab height (usefull for mobile)
                panel.style.height = 'auto';
                const naturalHeight = panel.offsetHeight;
                
                this.el.classList.add('-has-tab-open');
                panel.classList.add('-is-open');
                button.setAttribute('aria-expanded', 'true');
                button.setAttribute('aria-label', button.dataset.labelClose);
                panel.setAttribute('aria-hidden', 'false');
                this.tab.style.height = `${naturalHeight}px`;
                this.setupTabEvents(panel);
                this.activateTrap(panel);
            }
        }
        else {
            this.closeTab();
        }
    }

    closeTab() {

        this.$topLevelItem.forEach(item => {
            item.classList.remove('-masked');
        });

        if (this.tab) {
            const parentItem = this.tab.closest('.first-item');
            const button = parentItem ? parentItem.querySelector('button') : null;
            
            this.el.classList.remove('-has-tab-open');
            this.tab.classList.remove('-is-open');
            this.tab.setAttribute('aria-hidden', 'true');
            this.tab.style.height = '0px';
            
            if (button) {
                button.setAttribute('aria-expanded', 'false');
                button.setAttribute('aria-label', button.dataset.labelOpen);
            }
            
            this.removeTabEvents(this.tab);
            this.desActivateTrap();

            this.tab = null;
        }
        this.cancelClose();
    }

    resetAllTabs() {
        this.$firstItem.forEach(item => {
            const panel = item.querySelector('.header-panel');
            const button = item.querySelector('button');
            
            if (panel) {
                panel.classList.remove('-is-open');
                panel.setAttribute('aria-hidden', 'true');
                panel.style.height = '0px';

                // item.classList.remove('-masked');
            }
            
            if (button) {
                button.setAttribute('aria-expanded', 'false');
                button.setAttribute('aria-label', button.dataset.labelOpen);
            }
        });
    }


    // Tab hover events handling
    setupTabEvents(panel) {
        // Remove existing listeners
        this.removeTabEvents(panel);
        
        // Add new listeners
        this.mouseleaveHandler = () => this.scheduleClose();
        this.mouseenterHandler = () => this.cancelClose();
        
        panel.addEventListener('mouseleave', this.mouseleaveHandler);
        panel.addEventListener('mouseenter', this.mouseenterHandler);
    }

    removeTabEvents(panel) {
        if (this.mouseleaveHandler) panel.removeEventListener('mouseleave', this.mouseleaveHandler);
        if (this.mouseenterHandler) panel.removeEventListener('mouseenter', this.mouseenterHandler);
    }

    scheduleClose() {
        this.closeTimeout = setTimeout(() => this.closeTab(), 200);
    }

    cancelClose() {
        if (this.closeTimeout) {
            clearTimeout(this.closeTimeout);
            this.closeTimeout = null;
        }
    }

    // TrapFocus
    activateTrap() {
        this.trap.activate();
    }

    desActivateTrap() {
        this.trap.deactivate();
    }

    init() {
        this.initSticky();
        
        this.trap = focusTrap.createFocusTrap(this.el, {
            allowOutsideClick: true,
            initialFocus: false,
        });
        
        this.$firstItem.forEach(firstItem => {
            const panel = firstItem.querySelector('.header-panel');
            if (panel) {
                panel.style.height = '0px';
            }
        });
    }

    initSticky() {
        //this.el.style.setProperty('--header-height', `${this.el.offsetHeight}px`);
        this.st = ScrollTrigger.create({
            trigger: 'header',
            start: 'bottom top',
            endTrigger: '.main-content',
            end: 'bottom center',
            pin: 'header',
            pinSpacing: false,
            onRefresh: () => this.el.style.setProperty('--header-height', `${this.el.offsetHeight}px`),
            onLeaveBack: () => this.el.classList.remove('-sticky'),
            onEnter: () => this.el.classList.add('-sticky'),
            onUpdate: ({ direction }) => 
                direction === 1 ? this.el.classList.remove('-visible') : this.el.classList.add('-visible'),
        });
    }

    destroy() {

    }
}