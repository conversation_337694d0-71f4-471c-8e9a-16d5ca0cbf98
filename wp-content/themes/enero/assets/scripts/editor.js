wp.domReady( function() {

    wp.blocks.unregisterBlockStyle( 'core/quote', 'plain' );
    wp.blocks.unregisterBlockStyle( 'core/table', 'stripes' );
    wp.blocks.unregisterBlockStyle( 'core/image', 'rounded' );
    
    wp.blocks.unregisterBlockStyle( 'core/separator', 'dots' );
    wp.blocks.unregisterBlockStyle( 'core/separator', 'wide' );
    

    // Button

    wp.blocks.registerBlockStyle( 'core/button', [
		{
            name: 'primary',
			label: 'Primaire',
			isDefault: true,
        }
    ]);

    wp.blocks.registerBlockStyle( 'core/button', [
		{
            name: 'secondary',
			label: 'Secondaire',
			isDefault: false,
        }
    ]);

    wp.blocks.unregisterBlockStyle(
		'core/button',
		['outline', 'fill' ]
	);

    wp.blocks.registerBlockStyle( 'core/heading', [
		{
            name: 'with-line',
			label: 'Ave<PERSON> ligne',
			isDefault: false,
        }
    ]);
});