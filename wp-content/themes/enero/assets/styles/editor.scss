@use 'sass:math';

// -------- CONFIG --------
@import 'commons.scss';

// 2. Import fonts and icons
@import 'config/fonts';
@import 'config/icons';

// 3. Setup scss variables and mixins
@import 'config/variables';
@import 'common/shame';

// 4. Import custom block styles
@import 'blocks/content-sticky-ctn';
@import 'blocks/content-big-title';
@import 'blocks/content-statistics';
@import 'blocks/content-icons-block';
@import 'blocks/content-slider-testimonial';
@import 'blocks/content-slider-content';
@import 'blocks/content-download-links';

@import 'partials/cards/testimonial-card';
@import 'partials/cards/team-card';

@import 'partials/snippets/links';
@import 'partials/snippets/swiper-navigation';


// Import custom style for admin Login
@import 'pages/admin-login.scss';

div.editor-styles-wrapper {

  // 4. Import common styles
  // @import 'common/gutenberg';
  @import 'common/typography';

  // Adjust editor layout
  //width: 100%;
  //max-width: 80%;
  //margin: auto;
  //padding-bottom: 100px;

  // Reduce title sizes
  // h1 { @include fontSize(50px, 54px); }
  // h2 { @include fontSize(44px, 50px); }
  // h3 { @include fontSize(36px, 40px); }



  // 6. Override so it's correctly displayed in the editor

  // h1.editor-post-title {
  //   font-size: calc(var(--font-size-h1) / 1.5);
  //   line-height: calc(var(--line-height-h1) / 1.5);
  //   color: $dark-brown;
  // }

  // h2.block-editor-rich-text__editable {
  //   font-size: calc(var(--font-size-h2) / 1.5);
  //   line-height: calc(var(--line-height-h2) / 1.5);
  //   color: $brown;
  // } // and etc...
  
  // swiper
  .swiper-wrapper {
    display: flex;
  }

  .swiper-navigation {
    display: none !important;
  }
}

.is-style-with-line {
  &::before {
    width: 100% !important;
  }
}

// Custom blocks/Patterns

.group-text-image{
  .wp-block-columns {
    column-gap: grid-space(math.div(2,12));
  }
}

.group-background{
  .wp-block-columns {
    column-gap: grid-space(math.div(1,12));
  }
}

.content-statistics{
  .grid{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-medium);
  }
}

.image, .wp-block-image{
  clip-path: inset(0 0 0 0 round var(--border-radius-medium));
  transition: none;
  img{
      transform: scale(1);
  }
}

.wp-block-group.has-background:not([style*="padding"]) {
    padding: rs(40px, 72px) var(--spacing-medium);
    border-radius: var(--border-radius-medium);
    color: var(--color-text);
}

.wp-block-group.has-background:not([class*="-light-" i]) {
  @extend %dark-vars;
}