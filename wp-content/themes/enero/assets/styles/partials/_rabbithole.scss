.rabbithole {
    position: relative;
    display: flex;
    gap: rem(80px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: rem(240px);
    padding-bottom: rem(240px);

    @media (max-width: $tablet-lg) {
        padding-top: rem(160px);
        padding-bottom: rem(160px);
    }
    
    .texture {
        position: absolute;
        opacity: 12%;
        width: rem(700px);
        height: rem(700px);
        left: 100px;
        top: -100px;
        pointer-events: none;

        @media (max-width: $tablet-lg) {
            left: 30px;
        }
    }

    h2 {
        text-align: center;
        max-width: grid-space(math.div(8, 12), 0);

        @media (max-width: $tablet) {
            max-width: 100%;
        }
    }

    ul {
        max-width: grid-space(math.div(10, 12), 0);
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: rem(16px);
        @include noScrollBar();

        li {
            border: 2px solid var(--brand-blue-primary);
            border-radius: 12px;
            padding: rem(10px) rem(24px);
            transition: $transition;
            cursor: pointer;

            a {
                color: var(--brand-blue-primary);
                font-weight: 500;
                text-align: center;

            }
            &:hover {
                background-color: var(--brand-blue-primary);
                a { color: $white; }
            }
        }

        @media (max-width: $tablet) {
            max-width: 100vw;
            flex-wrap: nowrap;
            justify-content: flex-start;
            overflow-x: scroll;
            padding: 0 var(--container-margin);

            li a {
                white-space: nowrap;
            }
        }
    }
}