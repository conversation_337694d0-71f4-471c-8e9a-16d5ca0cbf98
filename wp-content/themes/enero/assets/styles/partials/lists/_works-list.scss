.works-list {

    .article {
        position: relative;
        cursor: pointer;
        height: 100%;

        &:hover {
            .image {
                filter: grayscale(50%);

                img {
                    transform: scale(1.05);
                }
            }
        }

        .-glass-tag {
            top: rem(24px);
            right: rem(24px);
            position: absolute;
        }
    }
    
    .image {
        overflow: hidden;
        border-radius: $radius-md;
        aspect-ratio: 1.14/1; // 638x560
        
        &:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 100%);
            z-index: 1;
        }

        img { 
            transition: $transition;
            @include img(); 
        }
    }

    .title {
        margin: 0;
        position: absolute;
        bottom: rem(40px);
        left: rem(40px);
    }
}