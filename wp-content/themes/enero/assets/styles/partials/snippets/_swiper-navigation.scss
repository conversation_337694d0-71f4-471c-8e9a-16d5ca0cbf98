.swiper-navigation {
    display: flex;
    justify-content: flex-end;
    gap: rem(5px);
    
    .prev, .next {
        border: 1px solid var(--border-default);
        border-radius: var(--border-radius-small);
        width: rem(64px);
        height: rem(64px);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: $transition;
        cursor: pointer;

        &:hover {
            background-color: var(--color-btn-primary);
            i {
                color: var(--color-btn-circle-primary);
            }
        }
        
    }

    i {
        color: var(--color-btn-primary);
        font-size: rem(20px);
        transition: $transition;
    }
}