/**
  * This file contains code related links and buttons
  */

a,
button,
input[type="submit"],
.wp-block-button__link {
  position: relative;
  display: inline-block;
  outline: none;
  border: 0 none;
  background: none;
  text-decoration: none;
  font-weight: 400;
  transition: $transition;

  &::before, &::after { transition: $transition; }

  // Default <a> styles
  &.link {
    // font-size: rs(14px, 16px, $desktop-lg);
    // font-weight: 500;
    // line-height: rs(14px, 16px, $desktop-lg);
    // letter-spacing: 1px;
    // text-transform: uppercase;
  }

  // Real button
  &.btn {
    color: var(--color-btn-text);
    font-size: var(--btn-size);
    font-family: var(--font-cta);
    font-weight: 500;
    letter-spacing: -0.6px;
    line-height: 100%;
    padding: rem(16px) rem(60px) rem(16px) rem(24px);
    display: flex;
    align-items: center;
    gap: rem(16px);
    text-decoration: none;
    border-radius: 100px;
    text-align: center;
    position: relative;
    width: fit-content;

    &:before,
    &:after {
      content: '';
      position: absolute;
      right: rem(16px);
      width: rem(24px);
      height: rem(24px);
      border-radius: 100%;
    }

    // Background circle
    &:before {
      background-color: var(--color-btn-circle-primary);
      transform: scale(1);
    }

    // Icon
    &:after {
      content: '\e90f';
      font-family: "icomoon" !important;
      color: $blue-dark;
      font-size: rem(9px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      line-height: 1;
    }

    
    &:hover {
      cursor: pointer;
      
      &:before {
        transform: scale(1.33);
      }
    }

    // auto add margin when stacked
    &:not(:only-of-type) {
      margin-bottom: 20px;
    }
  }

  &.-primary {
    background-color: var(--color-btn-primary);
    color: var(--color-btn-text);
    // border: rs(1px, 1px) solid $blue-primary;
    &:after {
      color: var(--color-btn-primary);
    }
    
    &:hover { 
      color: var(--color-btn-text) !important;
    }
  }
  
  &.-secondary {
    background-color: transparent;
    color: var(--color-btn-text-secondary);
    border: rs(1px, 1px) solid var(--border-default);
    
    &:before {
      background-color: var(--color-btn-circle-secondary);
    }

    &:after {
      color: var(--color-btn-circle-primary);
    }

    &:hover { 
      color: var(--color-btn-text-secondary) !important;
    }
  }

  &.-tertiary {
    color: $blue-dark;
    padding-right: rem(72px);


    &:before, &:after {
      // position: a;
    }

    &:before {
      width: rem(40px);
      height: rem(40px);
      border-radius: 12px;
      background-color: transparent;
      border: rs(1px, 1px) solid rgba(87, 87, 86, 0.40);
      right: rem(12px);
    }
    
    &:after {
      font-size: rem(12px);
      right: rem(20px);
    }

    &:hover {
      
      &:before {
        border-color: $blue-primary;
        background-color: $blue-primary;
        transform: scale(1) !important;
      }

      &:after {
        color: $white !important;
      }
    }

    &.-back {
      padding-left: rem(54px);
      padding-right: rem(24px);
      &::before {
        left: 0;
      }

      &::after {
        content: '\e910';
        color: var(--brand-blue-primary);
        left: rem(8px);
        font-size: rem(16px);
      }
    }
  }

  &.-clean-border {
    border: none;
  }
}


body:not(.wp-admin) a {
  // Extetnal icon
  &[target=_blank]:not(.-clean), &[href$=".pdf"]:not(.-clean), &[href$=".doc"]:not(.-clean){
    &:after {
        font-family: "icomoon";
        position: relative;
        display: inline-block;
        content: "\e90f"; // external link icon
        vertical-align: middle;
        margin-left: 5px;
        font-size: 9px;
        transition: transform 0.2s ease;
        transform: translateX(0px);
        padding-right: 5px;
        // line-height: 1;
    }

    &:hover {
        &:after {
            transform: translateX(3px);
        }
    }

    &.-primary, &.-secondary {
      &:after{
        margin-left: 10px;
        font-size: 19px;
      }
    }
  }

  // Download icon 
  &[href$=".pdf"], &[href$=".doc"], &[download]{
    &:after{
      content: "\e905" !important; // download icon
    }
  }
}

// add margin to buttons
.wp-block-buttons:not(:only-of-type) {
  margin-bottom: 20px;
}

.wp-block-button {

  // Button
  .wp-block-button__link {
    @extend .-primary;
  }
  
  &.is-style-secondary .wp-block-button__link {
    @extend .-secondary;
  }
  
  // &.is-style-primary-reverse .wp-block-button__link {
  //   @extend .-primary-reverse;
  // }
  
  // &.is-style-secondary-reverse .wp-block-button__link {
  //   @extend .-secondary-reverse;
  // }
}

.wp-block-file{
  .wp-block-file__button{
    @extend .btn;
    @extend .-primary;
    display: inline-flex !important; 
  }
}

// force button to be full width on mobile
@media (max-width: 380px) {
  .wp-block-button, .wp-block-button__link { width: 100%; }
}
