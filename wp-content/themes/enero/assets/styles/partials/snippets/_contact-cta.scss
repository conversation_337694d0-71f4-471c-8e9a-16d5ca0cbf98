.contact-cta {
    background: $white;
    z-index: 9999;
    color: $blue-navy;
    border-radius: 16px;
    padding: rem(16px) rem(24px);
    position: absolute;
    bottom: rem(80px);
    right: var(--container-margin);
    box-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.10);
    backdrop-filter: blur(8.100000381469727px);
    display: flex;
    align-items: center;
    transition: $transition;
    white-space: nowrap;

    &:hover {
        background-color: var(--brand-blue-primary);

        span, i {
            color: $white !important;
        }
    }

    span {
        font-weight: 500;
        font-family: var(--font-family-cta, Poppins);
        font-size: var(--font-size-cta-small, 16px);
        padding-right: rem(16px);
        border-right: solid 1px rgba(87, 87, 86, 0.40);
        transition: $transition;
    }

    
    i {
        padding-left: rem(16px);
        color: var(--brand-blue-primary);
        font-size: rem(22px);
        transition: $transition;
    }

    @media (max-width: $tablet) {
        display: none;
    }
}