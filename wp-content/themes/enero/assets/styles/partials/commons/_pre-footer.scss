.pre-footer {
    .inner {
        background: $blue-primary;
        padding: rem(64px);
        display: flex;
        justify-content: space-between;
        gap: rem(20px);
        min-height: rs(550px, 550px);
        border-radius: $radius-lg;
        position: relative;
        overflow: hidden;
    }

    .symbol {
        position: absolute;
        width: rs(850px, 850px);
        height: rs(850px, 850px);
        opacity: 0.2;
        z-index: 9;
        bottom: 0;

        img {
            @include img()
        }

    }

    .left {
        max-width: grid-space(math.div(7,12), 0);
        display: flex;
        flex-direction: column-reverse;

        h2 {
            color: $white;
        }
    }

    .right {
        max-width: grid-space(math.div(3,12), 0);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;

        .content {
            color: white;
        }

        .btn {
            background-color: $white;
            &:focus-visible{
                outline-color: $white;
            }
        }
    }

    @media (max-width: $desktop-sm) {
        .inner {
            padding: rem(40px) rem(24px);
            flex-direction: column;
            gap: rem(80px);
        }

        .left {
            flex-direction: column;
            max-width: unset;
        }

        .right {
            flex-direction: column;
            max-width: unset;
            gap: rem(40px);
        }
    }
}