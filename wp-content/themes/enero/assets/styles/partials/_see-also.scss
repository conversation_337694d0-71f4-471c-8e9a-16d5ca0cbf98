.see-also {
    .section-title {
        font-size: rem(23px);
        letter-spacing: 1px;
        text-transform: uppercase;
        color: rgba(23, 56, 80, 0.40);
        margin-bottom: rem(56px);
    }

    .posts-list {
        .post-item {
            padding-top: rem(40px);
            padding-bottom: rem(40px);
            border-top: 1px solid rgba(87, 87, 86, 0.40);
            transition: $transition;
            cursor: pointer;

            &:hover {
                background-color: $light-grey-50;
            }

            .content {
                display: flex;
                gap: 20px;
                flex-direction: column;
                justify-content: space-between;
            }
            
            .date {
                color: rgba(23, 56, 80, 0.40);
                text-transform: uppercase;
            }
            
            h2 {
                margin-top: rem(24px);
                a {
                    color: $blue-navy;
                }
            }

            .tags {
                display: flex;
                flex-wrap: wrap;
                gap: rem(8px);

                li {
                    border-radius: 8px;
                    padding: rem(2px) rem(12px);
                    border: 2px solid #1174CB;

                    a {
                        color: var(--brand-blue-primary)
                    }
                }
            }

            .image {
                aspect-ratio: 1/1;
                img {
                    @include img();
                }
            }

            @media (max-width: $tablet) {
                display: flex !important;
                gap: 16px;

                .content {
                    flex-basis: 70%;
                }

                .image {
                    flex-basis: 30%;
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}