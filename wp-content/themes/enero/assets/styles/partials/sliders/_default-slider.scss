.default-slider {
    padding-top: rem(72px);
    padding-bottom: rem(72px);

    .title-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: rem(16px);

        @media (max-width: $tablet) {
            flex-direction: column;
            align-items: flex-start;

            .btn {
                padding-left: 0;
            }
        }
    }

    .subtitle {
        margin-bottom: rem(40px) !important;
    }

    .swiper-wrapper {
        padding-top: rem(72px);
        padding-bottom: rem(72px);
    }

    .swiper-slide {
        cursor: pointer;

        &:hover {
            .image {
                &::before {
                    opacity: 1;
                }
                
                img {
                    transform: scale(1.05);
                }
            }
        }
        
        .image {
            aspect-ratio: 1/1;
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius-medium, 24px);
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 100%);

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.60) 100%);
                transition: $transition;
                opacity: 0;
            }

            // force remove default image reveal effect
            &.-reveal {
                clip-path: none;
            }

            img {
                @include img();
                transition: $transition;
            }

            .inner {
                position: absolute;
                display: flex;
                align-items: flex-end;
                top: 0;
                padding: rem(32px);
                width: 100%;
                height: 100%;
                z-index: 9;
            }

            .title {
                color: $white;
                font-size: rem(28px);
                line-height: 128%;
                letter-spacing: -0.308px;
                font-family: var(--font-title);
            }
        }

        .-glass-tag {
            position: absolute;
            top: rem(16px);
            right: rem(16px);
            z-index: 10;
        }
    }
}