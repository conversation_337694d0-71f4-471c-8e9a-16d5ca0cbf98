.filters-popup {
    position: fixed;
    width: 100vw;
    height: 100vh;
    visibility: hidden;
    top: 0;
    left: 0;
    background: rgba(3, 61, 86, 0.40);
    z-index: -1;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.4s ease, visibility 0s ease 0.5s, z-index 0s ease 0.5s;
    opacity: 0;

    &.-open{
        visibility: visible;
        z-index: 99999;
        opacity: 1;
        transition: opacity 0.4s ease 0.1s, visibility 0.1s ease;
        
        .wrapper {
            transition: transform 0.4s ease;
            transform: translateY(0);
        }
    }

    .overlay{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .wrapper {
        width: grid-space(math.div(6,12), 2);
        margin: 0 auto;
        background-color: $white;
        border-radius: 24px;
        position: relative;
        overflow: hidden;
        transition: transform 0s ease 0.6s;
        transform: translateY(rem(20px));
        z-index: 2;
    }

    @media (max-width: $tablet-lg) {
        position: fixed;
        
        .wrapper {
            width: 100%;
            margin-left: var(--container-margin);
            margin-right: var(--container-margin);
        }
    }

    // Overlay top
    .top {
        display: flex;
        justify-content: space-between;
        padding: 25px 24px;
        background-color: $white;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
        border-radius: 24px 24px 0px 0px;

        .popup-title { 
            font-size: 30px;
            margin: 15px 0;
        }

        .close {
            transform: rotate(45deg);
            i {
                display: block;
                color: $blue-dark;
                font-size: 20px;
            }
            cursor: pointer;
        }
    }

    // Overlay bottom and search button
    .bottom {
        padding: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $white;
        border-radius: 0px 0px 24px 24px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);

        .btn {
            &:after, &:before {
                display: none;
            }
            padding: rem(16px) rem(24px);

            &:hover {
                background-color: $blue-dark !important;
            }
        }
    }

    // Options container
    .options {
        padding: 11px 0;
        overflow-y: scroll;
        height: vh(50);

        @media (max-width: $tablet) {
            height: rem(300px);
        }

        // Checkbox option
        .option {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.10);
            transition: $transition;
            cursor: pointer;

            &:last-child {
                border-bottom: none;
            }
            
            &:hover {
                background-color: $light-grey;
            } 

            label {
                font-weight: 700;
                color: $blue-dark;
                transition: color 0.4s ease;
            }

            // hide input
            input[type="checkbox"] {
                position: absolute;
                height: 0;
                width: 0;

                &:focus-visible {

                    & + label {
                        color: $blue-dark !important;

                        &::before{
                            outline: 2px solid $blue-dark;
                        }
                    }
                }
            }

            // Custom checkbox
            input[type="checkbox"] + label {
                position: relative;
                padding-left: 44px;
                cursor: pointer;
                display: block;
                color: $blue-dark;
                font-weight: 400;
                font-family: var(--font-title);

                &:before {
                    @include flex();
                    content: "\e904";
                    font-family: 'icomoon';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 24px;
                    height: 24px;
                    border-radius: 8px;
                    color: transparent;
                    font-size: 12px;
                    border: 2px solid $blue-dark;
                    transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
                }

            }

            input[type="checkbox"]:checked + label::before {
                background-color: $blue-dark !important;
                color: $white;

                &:after {
                    content: "";
                    position: absolute;
                    left: 4px;
                    top: 8px;
                    width: 10px;
                    height: 10px;
                }
            }

            // Label checked
            input[type="checkbox"]:checked + label {
                // color: $blue-dark !important;
            }
        }
    }
}