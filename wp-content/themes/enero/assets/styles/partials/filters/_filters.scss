.filters-ctn {

    padding-top: rem(30px);
    padding-bottom: rem(56px);

    & > .top {
        display: flex;
        justify-content: space-between;

        @media (max-width: $tablet) {
            flex-direction: column;
            gap: 15px;
        }
    }

    // Search
    .search-ctn {
        border-radius: 8px;
        overflow: hidden;
        background-color: transparent;
        display: flex;
        align-items: center;
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.5);
        margin-left: auto;
        position: relative;

        &:has(input:focus-visible) {
            outline-color: #0071BC;
            outline-style: solid;
            outline-width: 1px;
            outline-offset: 0.2em;

            input { outline: none; }
        }
        
        input {
            background: transparent;
            width: rem(450px);
            width: 400px;
            padding: 14px 0 15px 24px;
            border: none;
            text-overflow: ellipsis;
        }

        .search-btn {
            cursor: pointer;
        }

        .clear {
            position: absolute;
            right: 60px;
            cursor: pointer;

            &.-hide {
                display: none;
            }

            i {
                font-size: 15px;
                display: block;
            }
        }

        @media (max-width: $desktop-sm) {
            margin-left: unset;
        }

        @media (max-width: $tablet) {
            width: 100%;

            input {
                width: 100%;
            }
        }
    }

    .filters {
        display: flex;
        gap: rem(20px);
        justify-content: space-between;
        align-items: flex-end;

        @media (max-width: $tablet) {
            flex-direction: column-reverse;
            align-items: flex-start;

            .search-ctn {
                margin-top: 20px;
                width: 100%;
                
                input {
                    width: 100%;
                }
            }
        }
    }
    
    .select-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: rem(64px) rem(24px);
        width: 100%;

        @media (max-width: $desktop-sm) {
            gap: rem(24px);
        }
    }


    // Select filter
    .filter-select {

        &.-open {
            .filters-popup {
                @extend .-open;
            }
        }
        
        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .label {
            font-size: rem(17px);
            line-height: 1;
            font-weight: 500;
            cursor: pointer;
            transition: $transition;
            border-radius: 200px;
            padding: rem(12px) rem(16px) rem(12px) rem(32px);
            @include flex($gap: 24px, $justify: space-between);
            transition: $transition;
            background: transparent;
            color: var(--brand-blue-dark);
            border: 1px solid rgba(87, 87, 86, 0.40);
            width: fit-content;

            i {
                font-size: rem(15px);
                transform: rotate(90deg);
                border-radius: 100%;
                @include flex();
                color: $white;
                padding: rem(10px);
                background: $blue-primary;
                pointer-events: none;
            }

            &:hover {
                cursor: pointer;
                background-color: transparent;
                border-color: $black;
                color: $black;
            }
        }

        input:focus + .label {
            @include focus();
        }        
    }

    // Radio filter
     .filter-radio {
        display: flex;
        flex: 1 1 100%;
        overflow: scroll;

        margin-right: calc(var(--container-margin) * -1);
        padding-right: var(--container-margin);

        .option {
            padding: 0 28px;
            border-right: 1px solid rgba($color: #000000, $alpha: 0.4);

            &:last-child {
                border-right: none;
            }
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        label {
            display: flex;
            align-items: center;
            flex-direction: column;
            gap: 5px;
            cursor: pointer;
            transition: $transition;
            opacity: 0.6;

            span {
                font-size: 15px;
                font-weight: 700;
                letter-spacing: 0.5px;
                color: $black;
                text-align: center;
                text-wrap: nowrap;
                text-transform: uppercase;
                font-family: var(--font-title);
            }

            i {
                font-size: 28px;
            }

            &:hover {
                opacity: 1;
            }            
        }

        input:checked + label {
            opacity: 1;
        }
    }


    // Selected filters
    .selected-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 24px;

        input {
            position: absolute;
            opacity: 0;
            pointer-events: none;

            &:focus-visible {
                + label {
                    @include focus();
                }
            }
        }

        // Option
        button {
            background-color: $blue-primary;
            color: $white;
            border: 2px solid $blue-primary;
            border-radius: 25px;
            font-size: rem(17px);
            line-height: 1;
            font-weight: 500;
            cursor: pointer;
            transition: opacity 0.4s ease;
            padding: 11px 16px 13px;
            display: flex;
            align-items: flex-end;
            gap: 10px;

            &:hover {
                opacity: 0.8;
            }

            &::after {
                @include after();
                content: '\e90d'; // Need to change
                font-family: 'icomoon';
                position: relative;
                font-size: 13px;
                color: $white;
                display: inline-block;
                transform: rotate(45deg);
            }

        }
    }
}