.team-card{
    padding: rs(20px,24px);
    border: 1px solid var(--border-default);
    border-radius: var(--border-radius-medium);

    .profile-image{
        width: rem(96px);
        height: rem(96px);
        overflow: hidden;
        border-radius: 50%;
        position: relative;
        outline: 1px solid var(--border-default, rgba(87, 87, 86, 0.40));
        outline-offset: 6px;

        img{
            @include img();
            aspect-ratio: 1/1;
        }
    }

    .name{
        font-size: rem(22px);
        font-weight: 500;
        color: var(--color-title);
        letter-spacing: -0.6px;
        margin: rem(16px) 0 rem(6px) 0;
        
    }

    .contact-ctn{
        margin-top: rs(32px, 40px);
        display: inline-flex;
        flex-direction: column;
        gap: rem(16px);

        a{
            display: inline-flex;
            align-items: center;
            gap: rem(10px);
            color: var(--color-text);
            text-decoration: none;
            transition: $transition;

            span{
                color: var(--color-btn-primary)
            }

            &:hover{
                color: var(--brand-blue-primary);
            }
        }
    }
}