body.admin-bar {
  .header.-sticky.-visible{
    .wrapper {
      transform: translateY(calc(var(--header-height) + 32px));
    }
  }
}

header {
  z-index: 9999;
  position: relative;

  .wrapper {
    @include container;
    background-color: transparent;
    padding: rem(30) var(--grid-margin);
    display: flex;
    justify-content: space-between;
  }

  &.-has-tab-open {
    .wrapper:before {
      opacity: 1;
    }
  }


  &.-sticky {
    .wrapper {
      padding: rem(16px) var(--grid-margin);
      background: $white;
    }
  }

  &.-sticky.-visible{
    .wrapper {
      transform: translateY(var(--header-height));
    }
  }

  &.-blue-bg {
    .wrapper {
      background-color: $blue-navy;
    }
  }

  // Dark variant
  &.-dark:not(.-sticky) {
    .logo {
      .letters path {
        fill: #fff;
      }
    }

    .first-item {
      > a, > button {
        color: $white !important;
      }
    }

    .lang-switcher {
      .wpml-ls-link {
        color: $white !important;
      }
    }

    &.-open {
      .logo {
        .letters path {
          fill: $blue-dark;
        }
      }

      .first-item {
        > a, > button {
          color: var(--cta-primary) !important;
        }
      }

      .lang-switcher {
        .wpml-ls-link {
          color: var(--cta-primary) !important;
        }
      }
    }
  }

  .wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    transition: $transition;
  }

  .logo {
    max-width: rem(149px);
  }

  // Mobile
  @media (max-width: 1049px) {

    &.-open {

      .wrapper {
        height: 100dvh;
        background-color: $white;
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
      }

      .main-nav, .side {
        display: block;
      }

      // Side toggle
      .mobile-toggle {
        i::before {
          font-size: rem(16px);
          content: '\e902';
        }
      }
    }

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .wrapper {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      overflow: scroll;
      gap: 0;
      padding: rem(24px) var(--container-margin) rem(24px) var(--container-margin);
    }

    .main-nav {
      display: none;
      padding-top: rem(24px);

      button[aria-expanded="true"] {
          background-color: $bg;

          i {
              transform: rotate(180deg);
          }
      }

      .first-item {
        font-size: var(--font-size-cta-large);
        line-height: var(--line-height-cta-large);
        font-family: var(--font-cta);
        font-weight: 500;
        letter-spacing: -0.6px;
        position: relative;

        &::after{
          content: '';
          position: absolute;
          bottom: 0;
          left: calc(var(--container-margin) * -1);
          width: 100vw;
          height: 1px;
          background-color: rgba(87, 87, 86, 0.40);
        }
        
        > button, > a {
          margin: 0 calc(var(--container-margin) * -1);
          padding: rem(32px) var(--container-margin);
          color: var(--cta-primary);
          width: 100vw;
          display: flex;
          justify-content: space-between;
          align-items: center;

          i {
            display: block;
            color: var(--brand-blue-primary, #1174CB);
            font-size: rem(10px);
            transition: $transition;
          }
        }
      }
    }

    .side {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      gap: rem(32px);
      display: none;

      nav {
        .intro, .icon, button {
          display: none;
        }
      }

      .lang-switcher {
        display: none;
      }
    }

    .-mobile {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: rem(32px);
    }
    
    .mobile-toggle {
      cursor: pointer;
      border-radius: 100%;
      width: rem(40px);
      height: rem(40px);
      background: var(--brand-blue-primary, #1174CB);
      transition: $transition;

      &:hover {
        background-color: #173850;
      }

      i {
        display: flex;
        align-items: center;
        justify-content: center;
        color: $white;
        font-size: rem(20px);
      }
    }
  }

  // Desktop
  @media (min-width: 1050px) {

    .wrapper {
      &:before {
        content: '';
        height: 100lvh;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(2, 38, 61, 0.10);
        backdrop-filter: blur(1.5px);
        transition: $transition;
        opacity: 0;
        pointer-events: none;
      }
    }

    .-mobile {
      display: none;
    }

    .menu {
      display: flex;
      align-items: center;
      gap: rem(40px);
  
      // Main nav
      .first-item {

        &:has(.header-panel.-is-open) {
          .icon-arrow-small {
            transform: rotate(180deg);
          }
        }
  
        > a, > button {
          display: flex;
          align-items: center;
          gap: rem(8px);
          color: var(--cta-primary);
          font-weight: 400;
          letter-spacing: -0.8px;
          font-size: rem(17px);
          cursor: pointer;
          transition: $transition;
  
          // chevron icon
          .icon-arrow-small {
            display: block;
            color: var(--brand-blue-primary, #1174CB);
            font-size: rem(8px);
            transition: $transition;
          }
  
          &:hover {
            .icon-arrow-small {
              transform: rotate(180deg);
            }
          }
        }

        &.-masked > button, &.-masked > a{
          opacity: 0.7;
        }
      }
    }
  
    // Side toggle
    .side {
      display: flex;
      align-items: center;
      gap: rem(32px);
  
      .side-menu {
  
        &.-toggled {
          .first-item button i::before {
            font-size: rem(16px);
            content: '\e902';
          }
        }
        
        .first-item {
          button {
            
            i {
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 100%;
              width: rem(40px);
              height: rem(40px);
              background: var(--brand-blue-primary, #1174CB);
              color: $white;
              font-size: rem(20px);
              transition: $transition;
  
              &:hover {
                background-color: #173850;
              }
            }
          }
        }
      }
    }
  }


  // Lang 
  .lang-switcher {
    grid-column-start: 3;
    grid-column-end: 4;
    text-align: right;
    
    ul {
      display: flex;
      align-items: center;

    }

    .wpml-ls,.wpml-ls-link{
      padding: 0;
      text-transform: uppercase;
    }

    .wpml-ls-link {
      font-family: var(--font-title);
      color: var(--cta-primary);
      font-size: rem(16px);
      font-weight: 500;
      
      span { transition: $transition; }

      :hover{
        color: var(--brand-blue-primary, #1174CB);
      }
    }
  }
}