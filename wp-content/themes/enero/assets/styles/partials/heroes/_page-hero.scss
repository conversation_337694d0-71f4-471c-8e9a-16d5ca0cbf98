.page-hero {
    position: relative;
    background-color: var(--brand-light-grey);
    overflow: hidden;
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
    width: 100%;
    margin-bottom: rs(62px, 114px);
    
    .grid{
        padding: rs(40px, 72px) 0;
        border-top: 1px solid var(--border-default);
    }

    .main-title{
        margin: rs(40px, 40px) 0 rs(40px, 62px);
    }

    .btn-ctn{
        display: flex;
        flex-wrap: wrap;
        gap: rem(16px);
        margin: rem(38px) 0 0;

        .btn{
            margin: 0;
        }

    }

    .image{
        aspect-ratio: 640/520;
    }

    @media (max-width: $tablet-lg) {
        .image{
            order: -1;
            margin-bottom: rem(32px);
        }
    }
}