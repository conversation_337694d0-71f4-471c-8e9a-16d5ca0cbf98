.expertise-hero {
    background-color: var(--brand-blue-navy);
    padding-bottom: rem(72px);
    @extend %dark-vars;
    position: relative;
    overflow: hidden;

    .texture {
        position: absolute;
        opacity: 12%;
        width: rem(900px);
        height: rem(900px);
        right: 0;
        bottom: 0;
        pointer-events: none;

        @media (max-width: $tablet-lg) {
            left: 30px;
        }
    }
    
    .inner {
        padding-top: rem(72px);
        border-top: 1px solid rgba(255, 255, 255, 0.40);
    }

    .content {
        padding-right: grid-space(math.div(1,12), 0);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: rem(24px);

        .wrapper {

        }

        h1 {
            margin-top: rem(40px);
            margin-bottom: rem(64px);
        }

        .--eyebrow {
            color: $white;
        }

        .text {
            color: $white-80;
        }
    }

    .image {
        aspect-ratio: 1/1;

        img {
            @include img();
        }
    }


    .tag-list {
        display: flex;
        gap: rem(8px);
        flex-wrap: wrap;
        margin-top: auto;
    }


    @media (max-width: $tablet-lg) {

        h1 {
            margin: rem(40px) 0;
        }
        
        .content {
            order: 2;
        }

        .image {
            order: 1;
        }

        .tag-list {
            flex-wrap: nowrap;
            overflow-x: scroll;
            @include noScrollBar();
            margin-right: calc(-1 * var(--container-margin));
            margin-left: calc(-1 * var(--container-margin));
            padding-right: var(--container-margin);
            padding-left: var(--container-margin);
        }
    }
}