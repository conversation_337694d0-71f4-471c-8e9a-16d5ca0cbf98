.content-accordion-ctn {
    @include grid(1);
    margin: var(--spacing-medium) 0;

    .accordion-item{
        border: 1px solid var(--border-default);
        border-radius: var(--border-radius-medium); 
        background-color: $white;

        .accordion-header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: rem(20px);
            padding: rs(20px, 40px) var(--spacing-medium);

            &:focus-visible{
                border-radius: var(--border-radius-medium); 
            }


            &:hover {
                cursor: pointer;
            }

            .accordion-title{
                @extend h5;
                margin: 0;
                color: var(--color-title);
                text-align: left;
            }
        }

        .icon-plus {
            @include flex();
            font-size: rem(20px);
            &::after {
                @include icon('\e90d');
            }

            &::after {
                @include icon('\e903');
            }

            &::after, &::before {
                color: var(--brand-blue-primary);
            }
        }

        .accordion-content {
            visibility: hidden;
            transition: height 1s $default-easing, visibility 1s ease;
            overflow: hidden;
            padding: 0 var(--spacing-medium);

            & > .inner{
                padding-top: rem(24px);
                
                & > *:first-child { margin-top: 0; } // Remove any additional margin at the beginning of content
        
                > *:last-child { padding-bottom: rs(24px, 42px); } // Add padding at the end of content
            
            }
        }

        &.-open {

            .accordion-content{
                visibility: visible;
            }

            .icon-plus {
                &::before { opacity: 0; }
            }
        }
    }

    @media (max-width: $mobile-lg) {
        .accordion-item{
            border-radius: var(--border-radius-small);
            .accordion-header{
                padding: rem(24px) rem(20px);
            }

            .accordion-content{
                padding-left: rem(20px);
                padding-right: rem(20px); 
            }
        }
    }
}