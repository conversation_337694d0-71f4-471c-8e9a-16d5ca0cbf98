.content-icons-blocks{
    @include flex(flex-start, space-between, column);
    row-gap: var(--spacing-medium);
    background-color: var(--bg-light);
    padding: rs(24px, 32px);
    border-radius: var(--border-radius-medium);
    min-height: rs(200px, 320px);

    .icon {
        max-width: rs(90px, 130px);
    }

    .text{
        @extend .-h6;
        color: $base-700 !important;
        width: calc(100% - rem(40px));
    }
}

.has-background .content-icons-blocks{
    --bg-light: #{$white};
}