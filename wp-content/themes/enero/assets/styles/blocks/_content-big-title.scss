.content-big-title{
    margin: var(--spacing-medium) 0;
    min-height: vh(100);
    @include flex(center,center, column);
    position: relative;
    overflow: hidden;

    .symbol{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url(../images/textures/texture_circle_full.webp);
        background-repeat: no-repeat;
        background-size: contain;
        opacity: 0;
        pointer-events: none;
        transition: opacity 1s ease 0.2s;

        &.-show{
            opacity: 0.1;
        }
    }

    h2{
        color: $blue-primary !important;
        font-size: rs(64px, 88px) !important;
        max-width: grid-space(math.div(8,12), 0);
        text-align: center;
    }

    @media (max-width: $tablet-lg) {
        min-height: vh(60);
        h2{
            max-width: 100%;
        }
    }
}

.acf-block-preview{
    .content-big-title{
        .symbol{
            opacity: 0.1;
        }
    }
}