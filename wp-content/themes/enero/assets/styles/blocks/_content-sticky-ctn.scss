.content-sticky-ctn {
    --bg-head: #{$white-80};

    .content-sticky-title{
        padding-top: rem(24px);
        padding-bottom: rem(24px);
        border-bottom: 1px solid var(--border-default);
        margin-bottom: 0;
        z-index: 10;

        h2{
            @extend .title-section;
            margin: 0;
        }
    }

    .content-sticky-image{
        padding: rs(36px, 56px);
        position: relative;
        height: auto;

        .image-sticky{
            &::after{
                content: '';
                position: absolute;
                top: 0;
                left: calc( var(--container-margin) * -1 - rs(36px, 56px));
                width: 120%;
                height: 120%;
                background-image: url(../images/textures/media_left_texture.webp);
                background-repeat: no-repeat;
                background-position: right bottom;
                background-size: contain;
                opacity: 0.1;
                z-index: -1;
            }
        }
    }

    .content-sticky-content{
        padding: 0 rs(36px, 64px);
    }

    &.-image-right{

        .content-sticky-image{
            .image-sticky{
                &::after{
                    background-image: url(../images/textures/media_right_texture.webp);
                    right: calc( var(--container-margin) * -1 - rs(36px, 56px));
                    left: initial;
                }
            }
        }
    }

    &:not(.block-editor-block-list__block) {

        .content-sticky-title{
            &::before{
                content: '';
                position: absolute;
                top: 0;
                left: calc( var(--container-margin) * -1 );
                width: 100vw;
                height: 100%;
                background:rgb(from var(--bg-head) r g b / 0.2);
                backdrop-filter: blur(5px);
            }
        }

        & > .content-sticky-image{
            width: grid-space(math.div(5,12));
        }
        & > .content-sticky-content{
            width: grid-space(math.div(7,12));
        }
    }

    @media (max-width: $tablet-lg) {
        .content-sticky-inner{
            width: 100%;
            display: block;
        }

        .content-sticky-image{
            width: 100%;
            padding: 0;
            margin-top: rem(32px);
        }

        .content-sticky-content{
            width: 100%;
            padding: 0;
        }
    }
}


.has-brand-light-grey-background-color {
    .content-sticky-ctn {
        --bg-head: var(--wp--preset--color--brand-light-grey);
    }
}

.has-brand-blue-dark-background-color {
    .content-sticky-ctn {
        --bg-head: var(--wp--preset--color--brand-blue-dark);
    }
}

.has-brand-blue-navy-background-color {
    .content-sticky-ctn {
        --bg-head: var(--wp--preset--color--brand-blue-navy);
    }
}

.has-brand-blue-primary-background-color {
    .content-sticky-ctn {
        --bg-head: var(--wp--preset--color--brand-blue-primary);
    }
}

.has-brand-blue-steel-background-color {
    .content-sticky-ctn {
        --bg-head: var(--wp--preset--color--brand-blue-steel);
    }
}