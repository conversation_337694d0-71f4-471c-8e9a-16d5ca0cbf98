.content-statistics {
    margin: var(--spacing-medium) 0;

    .grid{
        row-gap: var(--spacing-medium);
    }

    .statistics{
        @include grid(2);

        &> .statistic-block:last-child:nth-child(odd) {
            grid-column: 1 / -1;
        }


        .statistic-block{
            .title{
                margin: 0;
                padding-bottom: rem(8px);
                border-bottom: 2px solid var(--color-sticky-title);
                color: var(--color-sticky-title);
                display: flex;
                align-items: flex-end;
                gap: rem(16px);

                span{
                    color: var(--color-sticky-title);
                }

                .label{
                    color: var(--color-sticky-title);
                    font-weight: 500;
                    margin-bottom: rem(16px);
                    line-height: 1;
                    font-size: var(--font-size-h5);
                    line-height: var(--line-height-h5);
                }

                .number{
                    line-height: 40px;
                    font-weight: 400;
                }
            }

            p{
                
            }
        }
    }

    @media (max-width: $mobile-lg) {
        .statistics{
            @include grid(1);
        }
    }
}