.content-dowload-links{
    margin: var(--spacing-medium) 0;
    border: solid 1px var(--border-default);
    border-radius: var(--border-radius-medium);
    background-color: $white;
    overflow: hidden;

    .inner-head{
        padding: rs(24px, 32px) rs(28px, 40px);
        background-color: var(--bg-light);

        p{
            @extend .-cta;
            margin: 0;
            text-transform: uppercase;
        }
    }

    .links{
        padding: rs(18px, 20px) rs(28px, 40px);

        .link-item{
            padding: rs(24px, 32px) 0;
            border-bottom: 1px solid var(--border-default);

            &:last-child{
                border-bottom: none;
            }

            a{
                @extend .-cta-large ; 
                color: var(--cta-primary);
                width: 100%;
                padding-right: rem(30px);

                &:after{
                    font-size: rem(12px) !important;
                    position: absolute !important;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    color: var(--brand-blue-primary);
                }

                &[href$=".pdf"], &[href$=".doc"], &[download]{
                    &:after{
                        font-size: rem(18px) !important;
                    }
                }
            }

            &:hover{
                cursor: pointer;
                a{
                    color: var(--brand-blue-primary);
                }
            }
        }
    }
}