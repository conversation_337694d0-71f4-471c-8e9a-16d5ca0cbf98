.content-slider-content{

    .swiper-wrapper{
        padding-top: var(--spacing-normal);
        height: auto;
    }
    

    .swiper-slide{
        padding: rs(20px, 40px);
        min-height: rs(320px, 460px);
        border-radius: var(--border-radius-small);
        border: solid 2px var(--color-btn-primary);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: auto;

        .title{
            @extend .-h3;
            color: var(--color-btn-primary);
            margin:0 0 rem(32px) !important;
        }

        .content{

        }
    }
}

.acf-block-preview{
    .content-slider-content{

        .swiper-wrapper{
            overflow: hidden;
            gap: rem(32px);
        }

        .swiper-slide{
            width: 33.33%;
           flex-shrink: 0;
        }
    }

    @media (max-width: $tablet-lg) {
        .content-slider-content{
            .swiper-slide{
                width: auto;
                &:not(:first-child){
                    display: none;
                }
            }
        }
    }
}