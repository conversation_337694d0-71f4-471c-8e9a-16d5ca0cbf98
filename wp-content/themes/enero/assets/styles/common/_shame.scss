/**
  * This file contains temporary css code that should probably be put elsewhere.
  * To enforce good coding practices, keep it as empty as possible.
  * For more information: https://csswizardry.com/2013/04/shame-css/
  */


.-no-space {
  margin: 0 !important;
  padding: 0 !important;
}

.-no-top-space {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.-no-top-margin {
  margin-top: 0 !important;
}

.-no-bottom-space {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.-spacing-small {
  margin: var(--spacing-small);
}

.-spacing-normal {
  margin: var(--spacing-normal);
}

.-spacing-medium {
  margin: var(--spacing-medium);
}

.-spacing-large {
  margin: var(--spacing-large);
}

.-center {
  text-align: center;
}

.-relative{
  position: relative;
}

.-no-list-style {
  list-style: none;
  padding-left: 0;
}

.alignfull:not(.wp-block-image) {
  margin-right: calc(var(--container-margin) * -1);
  margin-left: calc(var(--container-margin) * -1);
  overflow: hidden;
}

.swiper-button-disabled {
  opacity: .4;
  cursor: not-allowed;
  pointer-events: none;
}

.swiper-button-lock {
  display: none !important;
}

// hide default post.excerpt
.read-more {
  display: none;
}

.image, .wp-block-image{
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  clip-path: inset(0 0 0 100% round var(--border-radius-medium));
  will-change: clip-path;
  transition: clip-path 3s cubic-bezier(.2,.8,.2,1);

  img{
    @include img();
    transform: scale(1.1);
    transition: transform 1.8s cubic-bezier(.2,.8,.2,1);
    will-change: transform;
  }

  &.-reveal{
    clip-path: inset(0 0 0 0 round var(--border-radius-medium));
    img{
        transform: scale(1);
    }
  }
}

.-glass-tag {
  padding: rem(6px) rem(16px);
  font-family: var(--font-cta);
  color: $white;
  border-radius: 12px;
  position: relative;
  background: linear-gradient(0deg, rgba(2, 38, 61, 0.12) 0%, rgba(2, 38, 61, 0.21) 100%), rgba(255, 255, 255, 0.20);
  backdrop-filter: blur(6px);
  width: fit-content;
  white-space: nowrap;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(180deg, rgba(242, 242, 242, 0.20) 0%, rgba(129, 129, 129, 0.20) 41.42%, rgba(255, 255, 255, 0.20) 100%);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }
}

// Hide WPML stuff
.otgs-development-site-front-end,
.wpml-ls-statics-footer { display: none !important; }