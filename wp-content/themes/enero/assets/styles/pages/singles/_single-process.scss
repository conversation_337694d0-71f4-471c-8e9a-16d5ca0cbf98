body.single-process {

    .anchors-ctn {
        .inner {
            height: 100%;

            .ctn {
                padding: rem(40px) 0;
                background-color: var(--brand-light-grey);
                border-radius: var(--border-radius-medium);
            }

            li {
                padding: rem(24px) rem(40px);
                position: relative;

                a {
                    font-size: rem(18px);
                    color: var(--base-800);
                    font-weight: 500;
                }

                &.-selected {
                    border-left: 6px solid $blue-primary;
                    padding: rem(24px) rem(40px) rem(24px) rem(34px);

                    a { color: $blue-primary; }
                }
            }
        }
    }

    .gutenberg {
        padding: 0 rem(56px);

        @media (max-width: $tablet-lg) {
            padding: 0;
        }

        h2:first-of-type {
            margin-top: 0;
        }
    }

    .container.grid {
        @media (max-width: $tablet-lg) {
            gap: rem(120px) 0;
        }
    }
}