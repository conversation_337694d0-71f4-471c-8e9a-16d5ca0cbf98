body.single-expertise {
    .container.gutenberg {
        padding-top: rem(64px);
        position: relative;
        padding-bottom: rem(160px);

        @media (max-width: $tablet-lg) {
            padding-bottom: rem(60px);
        }

        &:before {
            content: '';
            position: absolute;
            background-color: $blue-navy;
            top: rem(-30px);
            left: 0;
            width: 100%;
            height: rem(32px);
            z-index: -1;
        }

        &:after {
            content: '';
            position: absolute;
            background-color: $white;
            top: rem(-30px);
            left: 0;
            width: 100%;
            height: rem(32px);
            z-index: 1;
            border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
        }

        @media (max-width: $tablet-lg) {
            padding-top: 0;
        }
    }
}