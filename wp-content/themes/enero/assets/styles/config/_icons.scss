/* Replace with Generated icomoon file
** https://icomoon.io/app/#/select
** Fonts files (.eot/.svg/ttf...) must be in fonts folder
*/

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-back-arrow:before {
  content: "\e910";
}
.icon-external:before {
  content: "\e90f";
}
.icon-minus:before {
  content: "\e903";
}
.icon-checkmark:before {
  content: "\e904";
}
.icon-download:before {
  content: "\e905";
}
.icon-facebook:before {
  content: "\e906";
}
.icon-mail-full:before {
  content: "\e907";
}
.icon-mail:before {
  content: "\e908";
}
.icon-phone:before {
  content: "\e909";
}
.icon-linkedin:before {
  content: "\e90a";
}
.icon-arrow-right:before {
  content: "\e90b";
}
.icon-arrow-left:before {
  content: "\e90c";
}
.icon-plus:before {
  content: "\e90d";
}
.icon-chat:before {
  content: "\e90e";
}
.icon-menu:before {
  content: "\e901";
}
.icon-close:before {
  content: "\e902";
}
.icon-arrow-small:before {
  content: "\e900";
}