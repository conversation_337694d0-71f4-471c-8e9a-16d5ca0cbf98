<?php
/**
 * Disable <PERSON><PERSON><PERSON> by template
 *
 */
function disable_gut<PERSON>( $can_edit ) {
	if ( ! ( is_admin() && ! empty( $_GET['post'] ) ) ) {
		return $can_edit;
	}
	if ( should_disable( $_GET['post'] ) ) {
		$can_edit = false;
	}

	return $can_edit;
}

function gutenberg_allowed_block_types( $allowed_blocks ) {

    // Blacklist
    $blacklist = [
		// Core widgets & legacy
		'core/legacy-widget',
		'core/widget-group',
		'core/archives',
		'core/avatar',
		'core/block',
		'core/calendar',
		'core/categories',
		'core/comment-author-name',
		'core/comment-content',
		'core/comment-date',
		'core/comment-edit-link',
		'core/comment-reply-link',
		'core/comment-template',
		'core/comments',
		'core/comments-pagination',
		'core/comments-pagination-next',
		'core/comments-pagination-numbers',
		'core/comments-pagination-previous',
		'core/comments-title',
		'core/footnotes',
		'core/home-link',
		'core/latest-comments',
		'core/latest-posts',
		'core/loginout',
		'core/navigation',
		'core/navigation-link',
		'core/navigation-submenu',
		'core/page-list',
		'core/page-list-item',
		'core/pattern',
		'core/post-author',
		'core/post-author-biography',
		'core/post-author-name',
		'core/post-comments-form',
		'core/post-content',
		'core/post-date',
		'core/post-excerpt',
		'core/post-featured-image',
		'core/post-navigation-link',
		'core/post-template',
		'core/post-terms',
		'core/post-title',
		'core/query',
		'core/query-no-results',
		'core/query-pagination',
		'core/query-pagination-next',
		'core/query-pagination-numbers',
		'core/query-pagination-previous',
		'core/query-title',
		'core/query-total',
		'core/read-more',
		'core/rss',
		'core/search',
		'core/site-logo',
		'core/site-tagline',
		'core/site-title',
		'core/social-link',
		'core/tag-cloud',
		'core/template-part',
		'core/term-description',
		'core/audio',
		'core/missing',
		'core/more',
		'core/nextpage',
		'core/pullquote',
		'core/social-links',
		'core/verse',
		'core/post-comments',

		// WPML
		'wpml/language-switcher',
		'wpml/navigation-language-switcher',

		// Cookiebot
		'cookiebot/cookie-declaration',

		// Yoast
		'yoast/faq-block',
		'yoast/how-to-block',
		'yoast-seo/breadcrumbs',
    ];

    // Get all registered blocks
    $all_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();

    // keep only block names
    $all_blocks = array_keys( $all_blocks );

	// Filter to remove blacklisted blocks
    $allowed_blocks = array_filter(
        $all_blocks,
        function( $block ) use ( $blacklist ) {
            return ! in_array( $block, $blacklist, true );
        }
    );

    // Reindex array so Gutenberg gets a clean zero-based array
    $allowed_blocks = array_values( $allowed_blocks );

    return $allowed_blocks;
}

/**
 * Templates and Page IDs without editor
 *
 */
function should_disable( $id = false ) {
	if ( empty( $id ) ) {
		return false;
	}
	$disabled_ids = [
		//get_option( 'page_on_front' ),
	];
	$disabled_types = [
		//'council_member',
	];
	$disabled_templates = [
		//'templates/landing.php',
	];

	return in_array( intval( $id ), $disabled_ids ) || in_array( get_post_type( $id ), $disabled_types ) || in_array( get_page_template_slug( $id ), $disabled_templates );
}


/**
 * Add custom patterns
 *
 */
function kryzaplate_register_patterns() {

	remove_theme_support( 'core-block-patterns' );

	register_block_pattern_category(
		'enero',
		array( 'label' => __( 'Enero', 'enero' ) )
	);

	register_block_pattern(
		'enero/custom-pattern',
		array(
			'title'       => __( 'Groupe texte/image', 'enero' ),
			'description' => _x( 'Groupe texte/image pattern', 'Groupe personnalisé Texte/image pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"className":"group-text-image","layout":{"type":"default"}} -->
			<div class="wp-block-group group-text-image"><!-- wp:separator -->
			<hr class="wp-block-separator has-alpha-channel-opacity"/>
			<!-- /wp:separator -->

			<!-- wp:columns {"verticalAlignment":"top"} -->
			<div class="wp-block-columns are-vertically-aligned-top"><!-- wp:column {"verticalAlignment":"top"} -->
			<div class="wp-block-column is-vertically-aligned-top"><!-- wp:heading -->
			<h2 class="wp-block-heading">Plusieurs modèles de disposition seront disponibles</h2>
			<!-- /wp:heading -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"top":"var:preset|spacing|medium"}}}} -->
			<p style="margin-top:var(--wp--preset--spacing--medium)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla bibendum vitae nisi at efficitur. Morbi nec velit purus. Mauris nec suscipit turpis. Interdum et malesuada fames ac ante ipsum primis in faucibus. Nulla et luctus augue, tincidunt efficitur mauris. Nam rhoncus mi quam, sit amet interdum sapien viverra sit amet. Praesent aliquam, tortor ac tincidunt maximus, tortor sapien scelerisque velit, in dictum enim velit at ante.</p>
			<!-- /wp:paragraph --></div>
			<!-- /wp:column -->

			<!-- wp:column {"verticalAlignment":"top","className":"column-image"} -->
			<div class="wp-block-column is-vertically-aligned-top column-image"><!-- wp:image {"id":119,"sizeSlug":"large","linkDestination":"none"} -->
			<figure class="wp-block-image size-large"><img src="/wp-content/themes/enero/assets/images/placeholder_enero.jpg" alt="" class="wp-image-119"/></figure>
			<!-- /wp:image --></div>
			<!-- /wp:column --></div>
			<!-- /wp:columns --></div>
			<!-- /wp:group -->',
		)
	);
	
	register_block_pattern(
		'enero/custom-pattern-2',
		array(
			'title'       => __( 'Groupe boite avec image', 'enero' ),
			'description' => _x( 'Groupe boite avec image pattern', 'Groupe personnalisé boite avec fond de couleur et image pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"className":"group-background","backgroundColor":"brand-blue-steel","layout":{"type":"constrained"}} -->
			<div class="wp-block-group group-background has-brand-blue-steel-background-color has-background"><!-- wp:columns -->
			<div class="wp-block-columns"><!-- wp:column {"width":"66.66%"} -->
			<div class="wp-block-column" style="flex-basis:66.66%"><!-- wp:heading -->
			<h2 class="wp-block-heading">Possiblité de boite avec image</h2>
			<!-- /wp:heading -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"top":"var:preset|spacing|normal"}}}} -->
			<p style="margin-top:var(--wp--preset--spacing--normal)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed nisl eros. Sed a sapien sapien. Nulla fermentum, erat et molestie tristique, felis nisl rutrum mauris, tempor consectetur nisi lorem non purus.</p>
			<!-- /wp:paragraph -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|normal"}}}} -->
			<p style="margin-bottom:var(--wp--preset--spacing--normal)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed nisl eros. Sed a sapien sapien. Nulla fermentum, erat et molestie tristique, felis nisl rutrum mauris, tempor consectetur nisi lorem non purus.</p>
			<!-- /wp:paragraph -->

			<!-- wp:buttons -->
			<div class="wp-block-buttons"><!-- wp:button {"className":"is-style-primary"} -->
			<div class="wp-block-button is-style-primary"><a class="wp-block-button__link wp-element-button">Bouton primaire</a></div>
			<!-- /wp:button -->

			<!-- wp:button {"className":"is-style-secondary"} -->
			<div class="wp-block-button is-style-secondary"><a class="wp-block-button__link wp-element-button">Bouton secondaire</a></div>
			<!-- /wp:button --></div>
			<!-- /wp:buttons --></div>
			<!-- /wp:column -->

			<!-- wp:column {"width":"33.33%"} -->
			<div class="wp-block-column" style="flex-basis:33.33%"><!-- wp:image {"id":119,"sizeSlug":"large","linkDestination":"none"} -->
			<figure class="wp-block-image size-large"><img src="/wp-content/themes/enero/assets/images/placeholder_enero.jpg" alt="" class="wp-image-119"/></figure>
			<!-- /wp:image --></div>
			<!-- /wp:column --></div>
			<!-- /wp:columns --></div>
			<!-- /wp:group -->',
		)
	);
	
	register_block_pattern(
		'enero/custom-pattern-3',
		array(
			'title'       => __( 'Section avec iconographie', 'enero' ),
			'description' => _x( 'Groupe section avec iconographie pattern', 'Groupe personnalisé section avec iconographie pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"className":"group-iconography","backgroundColor":"brand-light-grey","layout":{"type":"constrained"}} -->
			<div class="wp-block-group group-iconography has-brand-light-grey-background-color has-background"><!-- wp:columns -->
			<div class="wp-block-columns"><!-- wp:column -->
			<div class="wp-block-column"><!-- wp:heading -->
			<h2 class="wp-block-heading">Section avec <br>iconographie</h2>
			<!-- /wp:heading --></div>
			<!-- /wp:column -->

			<!-- wp:column -->
			<div class="wp-block-column"><!-- wp:paragraph -->
			<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed nisl eros. Sed a sapien sapien. Nulla fermentum, erat et molestie tristique, felis nisl rutrum mauris, tempor consectetur nisi lorem non purus.</p>
			<!-- /wp:paragraph --></div>
			<!-- /wp:column --></div>
			<!-- /wp:columns -->

			<!-- wp:group {"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|small"}}},"layout":{"type":"grid","columnCount":2,"minimumColumnWidth":null}} -->
			<div class="wp-block-group" style="margin-bottom:var(--wp--preset--spacing--small)"><!-- wp:acf/icons-blocks {"name":"acf/icons-blocks","data":{"icon":"/wp-content/themes/enero/assets/images/sample-icon.svg","_icon":"field_68c3084f7ef87","text":"Maximisation de la génération électrique sans impact sur le procédé","_text":"field_68c3087d7ef88"},"mode":"preview"} /-->

			<!-- wp:acf/icons-blocks {"name":"acf/icons-blocks","data":{"icon":371,"_icon":"field_68c3084f7ef87","text":"Maximisation de la génération électrique sans impact sur le procédé","_text":"field_68c3087d7ef88"},"mode":"preview"} /--></div>
			<!-- /wp:group -->

			<!-- wp:group {"style":{"spacing":{"margin":{"top":"0"}}},"layout":{"type":"grid","columnCount":3,"minimumColumnWidth":null}} -->
			<div class="wp-block-group" style="margin-top:0"><!-- wp:acf/icons-blocks {"name":"acf/icons-blocks","data":{"icon":371,"_icon":"field_68c3084f7ef87","text":"Maximisation de la génération électrique sans impact sur le procédé","_text":"field_68c3087d7ef88"},"mode":"preview"} /-->

			<!-- wp:acf/icons-blocks {"name":"acf/icons-blocks","data":{"icon":371,"_icon":"field_68c3084f7ef87","text":"Valorisation accrue de la biomasse comme source primaire d’énergie","_text":"field_68c3087d7ef88"},"mode":"preview"} /-->

			<!-- wp:acf/icons-blocks {"name":"acf/icons-blocks","data":{"icon":371,"_icon":"field_68c3084f7ef87","text":"Réduction des pertes thermiques via évents et condenseur","_text":"field_68c3087d7ef88"},"mode":"preview"} /--></div>
			<!-- /wp:group --></div>
			<!-- /wp:group -->',
		)
	);
	
	register_block_pattern(
		'enero/custom-pattern-4',
		array(
			'title'       => __( 'Module avec effet sticky', 'enero' ),
			'description' => _x( 'Module avec effet sticky pattern', 'Module avec effet sticky pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:kryzalid/sticky-content {"imageId":119,"imageUrl":"http://enero.local/wp-content/uploads/2025/09/placeholder_03.webp","title":"Module avec effet sticky"} -->
			<div class="wp-block-kryzalid-sticky-content content-sticky-ctn" data-module-sticky-content=""><div class="content-sticky-title"><h2 class="ctn">Module avec effet sticky</h2></div><div class="content-sticky-inner"><div class="content-sticky-image"><div class="image-sticky"><div class="wp-block-image"><img src="http://enero.local/wp-content/uploads/2025/09/placeholder_03.webp" alt=""/></div></div></div><div class="content-sticky-content"><div class="content-sticky-intro"><!-- wp:heading -->
			<h2 class="wp-block-heading">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</h2>
			<!-- /wp:heading -->

			<!-- wp:heading {"level":3} -->
			<h3 class="wp-block-heading">Hausse continue des prix de l’énergie</h3>
			<!-- /wp:heading -->

			<!-- wp:paragraph -->
			<p>L’industrie des pâtes et papiers subit une hausse marquée des prix du gaz naturel, de l’électricité et des produits chimiques. Ces ressources, essentielles à la production de vapeur, au séchage et au traitement des fibres, deviennent de plus en plus coûteuses. Cette pression financière nuit directement à la rentabilité et impose une gestion énergétique rigoureuse.</p>
			<!-- /wp:paragraph -->

			<!-- wp:heading {"level":3} -->
			<h3 class="wp-block-heading">Manque de visibilité sur la consommation en temps réel</h3>
			<!-- /wp:heading -->

			<!-- wp:paragraph -->
			<p>De nombreuses installations s’appuient encore sur des équipements obsolètes, souvent peu automatisés et sujets à des défaillances. Cette vétusté limite la capacité à stabiliser les procédés, réduit la flexibilité opérationnelle et complique l’atteinte des standards de qualité exigés par le marché.</p>
			<!-- /wp:paragraph -->

			<!-- wp:quote -->
			<blockquote class="wp-block-quote"><!-- wp:paragraph -->
			<p>Face à ces enjeux, il devenait impératif de doter la centrale d’une logique de contrôle avancé capable d’orchestrer les équipements de façon intégrée, en tenant compte des contraintes industrielles, économiques et environnementales.</p>
			<!-- /wp:paragraph --></blockquote>
			<!-- /wp:quote --></div></div></div></div>
			<!-- /wp:kryzalid/sticky-content -->',
		)
	);

	register_block_pattern(
		'enero/custom-pattern-5',
		array(
			'title'       => __( 'Module avec effet sticky (alternatif)', 'enero' ),
			'description' => _x( 'Module avec effet sticky pattern', 'Module avec effet sticky pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"align":"full","backgroundColor":"brand-light-grey","layout":{"type":"constrained"}} -->
			<div class="wp-block-group alignfull has-brand-light-grey-background-color has-background"><!-- wp:kryzalid/sticky-content {"imageId":119,"imageUrl":"/wp-content/uploads/2025/09/placeholder_03.webp","title":"Module avec effet sticky","imagePosition":"right","metadata":{"categories":["enero"],"patternName":"enero/custom-pattern-4","name":"Module avec effet sticky"}} -->
			<div class="wp-block-kryzalid-sticky-content content-sticky-ctn -image-right" data-module-sticky-content=""><div class="content-sticky-title"><h2 class="ctn">Module avec effet sticky</h2></div><div class="content-sticky-inner"><div class="content-sticky-image"><div class="image-sticky"><div class="wp-block-image"><img src="http://enero.local/wp-content/uploads/2025/09/placeholder_03.webp" alt=""/></div></div></div><div class="content-sticky-content"><div class="content-sticky-intro"><!-- wp:heading -->
			<h2 class="wp-block-heading">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</h2>
			<!-- /wp:heading -->

			<!-- wp:heading {"level":3} -->
			<h3 class="wp-block-heading">Hausse continue des prix de l’énergie</h3>
			<!-- /wp:heading -->

			<!-- wp:paragraph -->
			<p>L’industrie des pâtes et papiers subit une hausse marquée des prix du gaz naturel, de l’électricité et des produits chimiques. Ces ressources, essentielles à la production de vapeur, au séchage et au traitement des fibres, deviennent de plus en plus coûteuses. Cette pression financière nuit directement à la rentabilité et impose une gestion énergétique rigoureuse.</p>
			<!-- /wp:paragraph -->

			<!-- wp:heading {"level":3} -->
			<h3 class="wp-block-heading">Manque de visibilité sur la consommation en temps réel</h3>
			<!-- /wp:heading -->

			<!-- wp:paragraph -->
			<p>De nombreuses installations s’appuient encore sur des équipements obsolètes, souvent peu automatisés et sujets à des défaillances. Cette vétusté limite la capacité à stabiliser les procédés, réduit la flexibilité opérationnelle et complique l’atteinte des standards de qualité exigés par le marché.</p>
			<!-- /wp:paragraph -->

			<!-- wp:quote -->
			<blockquote class="wp-block-quote"><!-- wp:paragraph -->
			<p>Face à ces enjeux, il devenait impératif de doter la centrale d’une logique de contrôle avancé capable d’orchestrer les équipements de façon intégrée, en tenant compte des contraintes industrielles, économiques et environnementales.</p>
			<!-- /wp:paragraph --></blockquote>
			<!-- /wp:quote --></div></div></div></div>
			<!-- /wp:kryzalid/sticky-content --></div>
			<!-- /wp:group -->',
		)
	);
	
	register_block_pattern(
		'enero/custom-pattern-6',
		array(
			'title'       => __( 'Module avec effet sticky (alternatif)', 'enero' ),
			'description' => _x( 'Module avec effet sticky pattern', 'Module avec effet sticky pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"layout":{"type":"grid","columnCount":null,"minimumColumnWidth":"20rem"}} -->
			<div class="wp-block-group"><!-- wp:acf/team-card {"name":"acf/team-card","data":{"field_68c3412995220":"407","field_68c3417095221":{"field_68c341a295222":"Thibaut ","field_68c341b095223":"Directeur technique - Kryzalid","field_68c341e695225":"424-424-24928","field_68c3420c95226":"23","field_68c3423995227":"<EMAIL>"}},"mode":"preview"} /-->

			<!-- wp:acf/team-card {"name":"acf/team-card","data":{"field_68c3412995220":"407","field_68c3417095221":{"field_68c341a295222":"Jean Philippe","field_68c341b095223":"Directeur artistique - Kryzalid","field_68c341e695225":"424-424-4223","field_68c3420c95226":"23","field_68c3423995227":"<EMAIL>"}},"mode":"preview"} /-->

			<!-- wp:acf/team-card {"name":"acf/team-card","data":{"field_68c3412995220":"407","field_68c3417095221":{"field_68c341a295222":"Émy","field_68c341b095223":"Developpeur fullstack - Kryzalid","field_68c341e695225":"424-424-4233","field_68c3420c95226":"08","field_68c3423995227":"<EMAIL>"}},"mode":"preview"} /--></div>
			<!-- /wp:group -->',
		)
	);
}