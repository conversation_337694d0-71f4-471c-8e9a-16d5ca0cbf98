<?php
/**
 * If you want create Custom Gutenberg block.
 */

function register_custom_blocks() {
	// check function exists
	if ( function_exists( 'acf_register_block' ) ) {
		$acf_render_callback = 'acf_block_render_callback';

		// register a slider block
		acf_register_block(
			array(
				'name'				=> 'slider',
				'title'				=> __('Visionneuse'),
				'description'		=> __('Add a custom slider'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'images-alt',
			)
		);

		acf_register_block(
			array(
				'name'				=> 'big-title',
				'title'				=> __('Grand titre impactant'),
				'description'		=> __('Ajouter un grand titre impactant'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'megaphone',
				'keywords'			=> array( 'big', 'title', 'impactant' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'title' => 'Grand titre impactant',
						),
					),
				),
			)
		);

		acf_register_block(
			array(
				'name'				=> 'statistics',
				'title'				=> __('Section statistiques'),
				'description'		=> __('Ajouter une section statistique'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'chart-bar',
				'keywords'			=> array( 'big', 'title', 'impactant' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'title' => 'Nos chiffres clés',
							'intro' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl sit amet ultricies lacinia, nunc nisl ultricies nunc, sit amet ultricies nisl nisl sit amet nisl.',
							'bloc_stat' => array(
								array(
									'number' => '10',
									'label' => 'Projets terminés',
									'description' => 'Lorem ipsum dolor sit amet',
								),
								array(
									'number' => '20',
									'label' => 'Clients satisfaits',
									'description' => 'Lorem ipsum dolor sit amet',
								)
							),
						),
					),
				),
			)
		);

		acf_register_block(
			array(
				'name'				=> 'icons-blocks',
				'title'				=> __('Bloc(s) Icône/Texte'),
				'description'		=> __('Ajouter un ou plusieur bloc(s) avec icône et texte'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'layout',
				'keywords'			=> array( 'big', 'title', 'impactant' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'icon' => '/wp-content/themes/enero/assets/images/sample-icon.svg',
							'text' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl sit amet ultricies lacinia, nunc nisl ultricies nunc, sit amet ultricies nisl nisl sit amet nisl.',
						),
					),
				),
			)
		);

		acf_register_block(
			array(
				'name'				=> 'slider-testimonial',
				'title'				=> __('Visionneuse témoignages'),
				'description'		=> __('Ajouter une visionneuse de témoignages'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'star-filled',
				'keywords'			=> array( 'slider', 'testimonial', 'témoignage' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'title' => 'Témoignages',
							'testimonials' => array(
								array(
									'testimonial' => 'Chez Enero, je ne suis pas qu’un employé : je suis le printeur masqué ! Derrière ma cape (et parfois coincé derrière l’imprimante), j’ai trouvé un endroit où mes blagues sont accueillies aussi sérieusement que mes idées. On me confie des projets ambitieux, mais toujours dans une ambiance où l’on peut rire ensemble.',
									'author' => array(
										'name' => 'Monsieur Placeholder',
										'role' => 'Certain disent qu\'il serait le printeur masqué',
										'image' => array(
											'url' => get_stylesheet_directory_uri() . '/assets/images/monsieur-placeholder.png',
										),
									),
								),
							),
						),
					),
				)
			)
		);

		acf_register_block(
			array(
				'name'				=> 'slider-content',
				'title'				=> __('Visionneuse contenu'),
				'description'		=> __('Ajouter une visionneuse de contenu'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'columns',
				'keywords'			=> array( 'slider', 'contenu' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'title' => 'Module slider de contenu',
							'sliders' => array(
								array(
									'title' => 'Lorem ipsum dolor sit amet',
									'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc et elementum lorem. Donec efficitur rhoncus turpis, ut iaculis risus consequat sed.',
								),
								array(
									'title' => 'Lorem ipsum dolor sit amet',
									'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc et elementum lorem. Donec efficitur rhoncus turpis, ut iaculis risus consequat sed.',
								),
								array(
									'title' => 'Lorem ipsum dolor sit amet',
									'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc et elementum lorem. Donec efficitur rhoncus turpis, ut iaculis risus consequat sed.',
								),
							),
						),
					),
				)
			)
		);

		acf_register_block(
			array(
				'name'				=> 'team-card',
				'title'				=> __('Membre d\'équipe'),
				'description'		=> __('Ajouter une carte d\'équipe'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'businessman',
				'keywords'			=> array( 'équipe' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'profile_image' => array(
								'url' => get_stylesheet_directory_uri() . '/assets/images/monsieur-placeholder.png',
							),
							'info' => array(
								'name' => 'Monsieur Placeholder',
								'role' => 'Certain disent qu\'il serait le printeur masqué',
								'email' => '<EMAIL>',
								'phone_number' => '************',
								'phone_ext' => '23',
							),
						),
					),
				)
			)
		);

		acf_register_block(
			array(
				'name'				=> 'download-links',
				'title'				=> __('Liens téléchargeables'),
				'description'		=> __('Ajouter un groupe de liens téléchargeables'),
				'render_callback'	=> $acf_render_callback,
				'category'			=> 'formatting',
				'icon'				=> 'admin-links',
				'keywords'			=> array( 'download', 'links', 'téléchargement' ),
				"example"			=> array(
					'attributes' => array(
						'mode' => 'preview',
						'data' => array(
							'title' => 'Découvrez ce procédé l’oeuvre à travers nos études de cas',
							'links' => array(
								array(
									'detail' => array(
										'title' => 'Aenean dictum neque iaculis eleifend sollicitudin',
										'url' => '#',
									),
								),
								array(
									'detail' => array(
										'title' => 'Nullam ornare tellus sed bibendum mattis',
										'target' => '_blank',
										'url' => 'document.doc',
									),
								),
							),
						),
					),
				)
			)
		);
	}
}

/**
 *  This is the callback that displays the block.
 *
 * @param   array  $block      The block settings and attributes.
 * @param   string $content    The block content (emtpy string).
 * @param   bool   $is_preview True during AJAX preview.
 */
function acf_block_render_callback( $block, $content = '', $is_preview = false ) {
	$context = Timber::context();
	$context['block'] = $block;
	$context['fields'] = get_fields();
	$context['is_preview'] = $is_preview;

	

	$slug = str_replace( 'acf/', '', $block['name'] );

	Timber::render( '/views/blocks/content-' . $slug . '.twig', $context );
}
