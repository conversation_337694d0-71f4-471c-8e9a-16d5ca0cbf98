{% extends 'base.twig' %}

{% block header_class %}-dark -blue-bg{% endblock %}

{% block content %}
    {% include 'partials/heroes/expertise-hero.twig' with {
        title: post.title,
        subtitle: __('Expertise', 'enero'),
        image: post.thumbnail.src,
        excerpt: post.excerpt,
        tags: post.terms({taxonomy: 'industry'}),
    } %}

    <div class="container gutenberg">
        {{ post.content }}
    </div>

    {% include 'partials/rabbithole.twig' with {
        title: __('Découvrez nos expertises qui impactent cette industrie', 'enero'),
        items: related_expertises,
    } %}

     {% if related_works|length %}
        <div class="works-slider-wrapper container">
            {% include 'partials/sliders/default-slider.twig' with {
                title: __('Nos réalisations dans cette industrie', 'enero'),
                slides: related_works,
                subtitle: __('Réalisations', 'enero'),
                use_tax: true,
                use_cta: true,
            } %}
        </div>
    {% endif %}

    {# Pre-footer #}
    {% include 'partials/commons/pre-footer.twig' with {
        title: section_pre_footer.title ? section_pre_footer.title : options.prefooter.title,
        content: section_pre_footer.content ? section_pre_footer.content : options.prefooter.content,
        link: section_pre_footer.link ? section_pre_footer.link : options.prefooter.link,
        class: '-spacing-medium'
    } %}

{% endblock %}