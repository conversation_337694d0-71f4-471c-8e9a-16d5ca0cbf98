{% extends 'base.twig' %}

{% block header_class %}{% endblock %}

{% block content %}
    {% include 'partials/heroes/process-hero.twig' with {
        title: post.title,
        excerpt: post.excerpt,
        image: post.thumbnail
    } %}

    {% if anchors|length > 0 %}
        <div class="container grid">
            <aside class="anchors-ctn col-12 col-t-lg-4" data-module-anchors>
                <div class="inner" data-module-absolute>
                    <div class="ctn">
                        <ul class="-no-list-style">
                            {% for index,title in anchors %}
                                <li>
                                    <a href="#{{ index }}" class="-tag" data-anchors="link">{{ title }}</a>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </aside>

            <div class="gutenberg col-12 col-t-lg-8">
                {{ post.content }}
            </div>
        </div>
    {% else %}
        <div class="container -narrow grid">
            <div class="gutenberg  col-12">
                {{ post.content }}
            </div>
        </div>
    {% endif %}

    {# Pre-footer #}
    {% include 'partials/commons/pre-footer.twig' with {
        title: section_pre_footer.title ? section_pre_footer.title : options.prefooter.title,
        content: section_pre_footer.content ? section_pre_footer.content : options.prefooter.content,
        link: section_pre_footer.link ? section_pre_footer.link : options.prefooter.link,
        class: '-spacing-medium'
    } %}

{% endblock %}