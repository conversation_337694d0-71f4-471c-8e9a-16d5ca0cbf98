{% extends 'base.twig' %}
{% block content %}

    {% include 'partials/heroes/hub-hero.twig' with {
        title: post.title,
        excerpt: post.excerpt,
        image: theme.link ~ '/assets/images/textures/texture_realisations.webp'
    } %}

    {# Ajax filterable list #}

    <div class="container" data-module-filter>

        <form id="ajax-form">
            <input type="hidden" id="ajax-settings" data-url="{{ url }}" data-limit="{{ limit ?: 20 }}">

            <div class="filters-wrapper grid">
                
                {# add your filter #}

                {% if filters %}
                    <div class="filters col-12 col-t-lg-6">
                        {% include 'partials/filters/filters.twig' with {
                            search: filters.search,
                            search_placeholder: filters.search_placeholder,
                            filters: filters.filters,
                        } %}
                    </div>
                {% endif %}

                {% if post.excerpt %}
                    <p class="excerpt -no-top-space col-12 col-t-lg-6">{{ post.excerpt }}</p>
                {% endif %}

            </div>

        </form>

        <div id="ajax-content">
            {% include 'partials/lists/loading.twig' %}

            <div class="ajax-container">
                <div class="inner">
                    {% if results is empty %}
                        {% include 'partials/lists/no-result.twig' %}
                    {% else %}
                        {% include 'partials/lists/works-list.twig' with {
                            results: results
                        } %}
                    {% endif %}
                </div>

            </div>
        </div>
    </div>

    {# Pre-footer #}
    {% include 'partials/commons/pre-footer.twig' with {
        title: section_pre_footer.title ? section_pre_footer.title : options.prefooter.title,
        content: section_pre_footer.content ? section_pre_footer.content : options.prefooter.content,
        link: section_pre_footer.link ? section_pre_footer.link : options.prefooter.link,
        class: '-spacing-medium'
    } %}

{% endblock %}