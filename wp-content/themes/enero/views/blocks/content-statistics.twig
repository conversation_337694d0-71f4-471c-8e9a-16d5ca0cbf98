{% set title = fields["title"] ?: block['data']['title'] %}
{% set intro = fields["intro"] ?: block['data']['intro'] %}
{% set statistics = fields["bloc_stat"] ?: block['data']['bloc_stat'] %}
<div class="content-statistics">
    <h2 class="is-style-with-line">{{ title }}</h2>
    <div class="grid">
        <div class="col-12 col-t-lg-4">
            <div class="intro">
                {{ intro }}
            </div>
        </div>
        <div class="col-12 col-t-lg-7 col-offset-t-lg-5 col-d-sm-6 col-offset-d-sm-6">
            <div class="statistics">
                {% for statistic in statistics %}
                    <div class="statistic-block">
                        <h3 data-module-mask-title class="title"><span class="number -h1 -no-space">{{ statistic.number }}</span> <span class="label">{{ statistic.label }}</span></h3>
                        <p data-scroll class="--small slide-in">{{ statistic.description }}</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>