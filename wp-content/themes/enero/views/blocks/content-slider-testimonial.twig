{% set slides = fields["testimonials"] ?: block['data']['testimonials'] %}
{% set title = fields["title"] ?: block['data']['title'] %}

{% extends 'partials/commons/skeleton-slider.twig' %}

{% block options %}
    class="content-slider-testimonial grid"
    data-s-speed="500" 
    data-loop="false" 
    data-slides-per-view="1"
    data-effect="slide" 
{% endblock %}

{% block title %}
    <div data-scroll class="title-ctn slide-in">
        <h2 class="-no-top-margin is-style-with-line">{{ title }}</h2>
    </div>
{% endblock %}

{% block nav %}
    <div class="swiper-navigation">
        <button class="prev" data-swiper="prev" title="{{ __('Previous slide', 'enero') }}"><i class="icon-arrow-left"></i></button>
        <button class="next" data-swiper="next" title="{{ __('Next slide', 'enero') }}"><i class="icon-arrow-right"></i></button>
    </div>
{% endblock %}

{% block slides %}
    {% for slide in slides %}
        <div tabindex="0" class="swiper-slide">
            {% include 'partials/cards/testimonial-card.twig' with {
                'testimonial': slide.testimonial,
                'image': slide.author.image.url,
                'name': slide.author.name,
                'role': slide.author.role
            } %}
        </div>
    {% endfor %}
{% endblock %}
