{% set title = fields["title"] ?: block['data']['title'] %}
{% set links = fields["links"] ?: block['data']['links'] %}

<div data-scroll class="content-dowload-links fade-in">
    <div class="inner-head">
        <p class="title -no-space">{{ title }}</p>
    </div>
    <div class="links">
        {% for link in links %}
            <div data-scroll class="link-item jsBlockLink slide-in">
                <a href="{{ link.detail.url }}" target="{{ link.detail.target ? link.detail.target : '_blank' }}" class="">{{ link.detail.title }}</a>
            </div>
        {% endfor %}
    </div>
</div>