<div class="see-also">
    <p class="container section-title">{{ __('À lire aussi', 'enero') }}</p>

    <div class="posts-list">
        {% for post in posts %}
            <article class="post-item jsBlockLink container grid">
                <div class="col-10 col-t-lg-10 content">
                    <div>
                        <p class="-no-space --meta date">{{ post.date|date('j F Y') }}</p>
                        <h2 class="-small -no-bottom-space">
                            <a href="{{ post.link }}">{{ post.title }}</a>
                        </h2>
                    </div>
                    <ul class="tags -no-list-style">
                        {% for tag in post.categories({taxonomy: 'category'}) %}
                            <li><a class="-tag" href="{{ tag.link }}">{{ tag.name }}</a></li>
                        {% endfor %}

                        {% for tag in post.terms({taxonomy: 'industry'}) %}
                            <li><a class="-tag" href="{{ tag.link }}">{{ tag.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="image col-2 col-t-lg-2">
                    <img src="{{ post.thumbnail.src ? post.thumbnail.src : theme.link ~ '/assets/images/placeholder.png' }}" alt="{{ post.thumbnail.alt }}">
                </div>
            </article>
        {% endfor %}
    </div>
</div>