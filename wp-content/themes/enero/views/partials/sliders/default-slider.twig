{% extends 'partials/commons/skeleton-slider.twig' %}

{% block options %}
    class="default-slider"
    data-s-speed="500" 
    data-loop="false" 
    data-slides-per-view="1.2" 
    data-space-between="var(--grid-gutter)"
    data-breakpoints='{
        "1024":{"slidesPerView":3.2}, 
        "768":{"slidesPerView":2.2}, 
        "320":{"slidesPerView":1.2}
    }'
{% endblock %}

{% block subtitle %}
    {% if subtitle %}
        <p class="-no-top-space --eyebrow">{{ subtitle }}</p>
    {% endif %}
{% endblock %}

{% block title %}
    <div class="title-wrapper">
        <h2 class="-no-space">{{ title }}</h2>

        {% if use_cta %}
            {% include 'partials/snippets/link.twig' with {
                title: __('Voir toutes les réalisations', 'enero'),
                link: '/realisations',
                target: '',
                classes: '-tertiary'
            } %}
        {% endif %}
    </div>
{% endblock %}

{% block nav %}
    <div class="swiper-navigation">
        <button class="prev" data-swiper="prev" title="{{ __('Previous slide', 'enero') }}"><i class="icon-arrow-left"></i></button>
        <button class="next" data-swiper="next" title="{{ __('Next slide', 'enero') }}"><i class="icon-arrow-right"></i></button>
    </div>
{% endblock %}

{% block slides %}
		{% for slide in slides %}
			<div class="swiper-slide">
				<div class="image">
                    {% set primary_taxonomy = fn('get_primary_taxonomy', slide.ID, 'industry') %}
                    {% if use_tax and primary_taxonomy %}
                        <span class="-glass-tag">{{ primary_taxonomy }}</span>
                    {% endif %}

                    <img src="{{ slide.thumbnail.src ? slide.thumbnail.src : theme.link ~ '/assets/images/placeholder.png' }}" alt="">
                    <div class="inner">
                        <a href="{{ slide.link }}">
                            <p class="title -no-space">{{ slide.title }}</p>
                        </a>
                    </div>
                </div>
			</div>
		{% endfor %}
{% endblock %}

