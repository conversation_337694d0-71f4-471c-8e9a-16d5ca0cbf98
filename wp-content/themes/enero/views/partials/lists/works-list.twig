<div class="works-list grid">
    {% for post in results %}
        <article class="article jsBlockLink col-12 col-t-lg-6">
            <div class="image">
                <img src="{{ post.thumbnail ? post.thumbnail : theme.link ~ '/assets/images/placeholder.png' }}" alt="">
            </div>
            <h3 class="title">
                <a href="{{ post.link }}">{{ post.title }}</a>
            </h3>
            <span class="-glass-tag">{{ fn('get_primary_taxonomy', post.ID, 'industry') }}</span>
        </article>
    {% endfor %}
</div>

{% include 'partials/lists/pagination.twig' with {
    'elements': results.pagination(4) 
} %}