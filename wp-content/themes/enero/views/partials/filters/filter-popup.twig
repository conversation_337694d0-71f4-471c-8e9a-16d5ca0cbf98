<div class="filters-popup" role="dialog" aria-modal="true" aria-labelledby="popup-{{ filter.id }}-title" data-select="popup" id="popup-{{ filter.id }}" aria-hidden="true">
    <span class="toggle overlay" data-select="group"></span>

    <div class="wrapper">
        <div class="top">
            <p id="popup-{{ filter.id }}-title" class="-h4 -no-space popup-title">{{ filter.label }}</p>
            <button type="button" class="toggle close"  aria-label="{{ __('Fermer la fenêtre','enero')}}" data-select="group">
                <i class="icon-plus"></i>
            </button>
        </div>

        <div class="options">
            {% for option in filter.options %}
                <div class="option">
                    <input type="checkbox" id="{{ filter.id ~ '-' ~ option.value }}" data-select="option" name="{{ filter.id }}" value="{{ option.value }}">
                    <label for="{{ filter.id ~ '-' ~ option.value }}" class="-cta-large">
                        {{ option.label }}
                    </label>
                </div>
            {% endfor %}
        </div>

        <div class="bottom">
            {% include 'partials/snippets/link.twig' with {
                title: __('Filtrer', 'enero'),
                link: '#',
                classes: '-primary toggle" data-select="group',
            } %}
        </div>
    </div>
</div>