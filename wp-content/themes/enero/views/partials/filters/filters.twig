<div data-scroll class="filters-ctn fade-in" data-module-select>

    <div class="filters">

        <div class="select-wrapper">
            {% for filter in filters %}

                {# Select overlay #}
                {% if filter.type == 'select' %} 
                    <div class="filter-select">
                        <button type="button" class="label toggle -cta" data-select="group" tabindex="0">{{ filter.label }} 
                            <i class="icon-plus"></i>
                        </button>
                        {% include 'partials/filters/filter-popup.twig' with {
                            filter: filter
                        } %}
                    </div>

                {# Regular link #}
                {% elseif filter.type == 'radio' %}
                    <div class="filter-radio">
                        {% for option in filter.options %}
                            <div class="option">
                                <input type="radio" 
                                    id="{{ filter.id ~ '-' ~ option.value }}" 
                                    name="{{ filter.id }}" 
                                    value="{{ option.value }}"
                                    {% if loop.first and (params[filter.id] is empty or 'all' in params[filter.id]|split(',')) %}
                                        checked
                                    {% elseif option.value in params[filter.id]|split(',') %}
                                        checked
                                    {% endif %}
                                >
                                <label class="label toggle" for="{{ filter.id ~ '-' ~ option.value }}">
                                    {% if option.icon %}<i class="icon-{{ option.icon }}"></i>{% endif %}
                                    <span>{{ option.label }}</span>
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endfor %}

            {# Search box #}
            {% if search %}
                <div class="search-ctn">
                    <label for="search" class="sr-only">{{ __('Rechercher', 'sqdi') }}</label>
                    <input type="text" name="search" id="search" data-select="search" placeholder="{{ search_placeholder }}" value="">
                    <button type="button" data-select="clear" class="clear -hide" aria-label="{{__('Éffacer','sqdi')}}">
                        <i class="icon-clear">|</i>
                    </button>
                    <button data-select="searchBtn" class="search-btn" aria-label="{{__('Rechercher','sqdi')}}">
                        <i class="icon-search">|</i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>


    {# Selected filter #}
    <div class="selected-row" data-select="selected-row">
        {% for filter in filters %}
            {% if not filter.skip %}
                {% for option in filter.options %}
                    {% if (option.value in params[filter.id]|split(',')) %}
                        <button data-target="{{ filter.id ~ '-' ~ option.value }}" tabindex="0" aria-label="{{__('Retirer le filtre: ' ~ option.label,'sqdi')}}" data-select="uncheck">
                            {{ option.label }}
                        </button>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
    </div>

</div>
