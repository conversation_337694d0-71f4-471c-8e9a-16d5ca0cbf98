{"key": "group_68c325150311b", "title": "Gutenberg visionneuse témoignage", "fields": [{"key": "field_68c32515b96cf", "label": "Titre", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_68c32561b96d0", "label": "Témoignage(s)", "name": "testimonials", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Ajouter un élément", "rows_per_page": 20, "sub_fields": [{"key": "field_68c32583b96d1", "label": "Témoignage", "name": "testimonial", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": "", "placeholder": "", "new_lines": "", "parent_repeater": "field_68c32561b96d0"}, {"key": "field_68c325a9b96d2", "label": "Auteur·rice", "name": "author", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_68c32600b96d3", "label": "Photo de profil", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "thumbnail"}, {"key": "field_68c32625b96d4", "label": "Prénom et Nom", "name": "name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_68c32638b96d5", "label": "Rôle/fonction", "name": "role", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}], "parent_repeater": "field_68c32561b96d0"}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/slider-testimonial"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1757619806}