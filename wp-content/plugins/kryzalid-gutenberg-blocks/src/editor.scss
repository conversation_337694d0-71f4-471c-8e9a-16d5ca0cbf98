/* Styles pour l'éditeur des blocs Kryzalid */

/* Accord<PERSON><PERSON> - Styles éditeur */
.kryzalid-accordion {
    padding: 16px;

    .accordion-item {
        border: 1px solid var(--border-default, #ccc);
        border-radius: 4px;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .accordion-header {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px 32px;

            .accordion-toggle {
                min-width: 32px;
                height: 32px;
                font-size: 24px;
                color: var(--title-color, black);
                border: none;
                cursor: pointer;
            }

            .accordion-title {
                flex: 1;
                padding: 16px 0;
                margin: 0;
            }
        }

        .accordion-content {
            padding: 32px;
        }
    }
}