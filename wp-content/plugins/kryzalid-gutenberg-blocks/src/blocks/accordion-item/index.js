import { registerBlockType } from '@wordpress/blocks';
import { __ } from '@wordpress/i18n';

import edit from './edit';
import save from './save';

registerBlockType('kryzalid/accordion-item', {
    title: __('Élément d\'accordéon', 'kryzalid-gutenberg-blocks'),
    description: __('Un élément individuel d\'accordéon avec titre et contenu', 'kryzalid-gutenberg-blocks'),
    category: 'kryzalid-blocks',
    parent: ['kryzalid/accordion'],
    icon: 'list-view',
    supports: {
        html: false,
        reusable: false,
    },
    attributes: {
        title: {
            type: 'string',
            default: '',
        },
        isOpen: {
            type: 'boolean',
            default: false,
        },
    },
    edit,
    save,
});
