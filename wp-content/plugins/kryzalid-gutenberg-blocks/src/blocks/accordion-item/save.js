import { useBlockProps, RichText, InnerBlocks } from '@wordpress/block-editor';

export default function Save({ attributes }) {
    const { title } = attributes;
    const blockProps = useBlockProps.save();

    return (
        <div {...blockProps} data-accordion="accordion" className="accordion-item">
            <button
                className="accordion-header" 
                type="button"
                aria-expanded="false"
                data-accordion="button"
            >
                <RichText.Content 
                    tagName="span" 
                    value={title} 
                    className="accordion-title"
                />
                <span className="accordion-icon" aria-hidden="true">
                    <i className="icon-plus"></i>
                </span>
            </button>
            <div className="accordion-content" aria-hidden="true">
                <div className="inner">
                    <InnerBlocks.Content />
                </div>
            </div>
        </div>
    );
}
