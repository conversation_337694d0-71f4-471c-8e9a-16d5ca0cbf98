import { __ } from '@wordpress/i18n';
import { useBlockProps, RichText, InnerBlocks, BlockControls } from '@wordpress/block-editor';
import { Button, ToolbarGroup, ToolbarButton } from '@wordpress/components';
import { useDispatch } from '@wordpress/data';
// import { trash } from '@wordpress/icons';

export default function Edit({ attributes, setAttributes, clientId }) {
    const { title, isOpen } = attributes;
    const blockProps = useBlockProps();

    const { removeBlock } = useDispatch('core/block-editor');

    const TEMPLATE = [
        ['core/paragraph', { placeholder: 'Ajoutez votre contenu ici...' }]
    ];

    const toggleOpen = () => {
        setAttributes({ isOpen: !isOpen });
    };

    const removeAccordionItem = () => {
        removeBlock(clientId);
    };

    return (
        <>
            <BlockControls>
                <ToolbarGroup>
                    <ToolbarButton
                        icon="trash"
                        label={__('Supprimer cette section', 'kryzalid-gutenberg-blocks')}
                        onClick={removeAccordionItem}
                    />
                </ToolbarGroup>
            </BlockControls>

            <div {...blockProps}>
                <div className="accordion-item">
                    <div className="accordion-header">
                        <RichText
                            tagName="h5"
                            placeholder={__('Titre de la section', 'kryzalid-gutenberg-blocks')}
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            className="accordion-title"
                        />
                        <Button
                            className="accordion-toggle"
                            onClick={toggleOpen}
                        >
                            {isOpen ? '−' : '+'}
                        </Button>
                        <Button
                            icon="trash"
                            isDestructive
                            onClick={removeAccordionItem}
                            className="remove-item"
                            title={__('Supprimer cette section', 'kryzalid-gutenberg-blocks')}
                        >
                            
                        </Button>
                    </div>
                    {isOpen && (
                        <div className="accordion-content">
                            <InnerBlocks
                                allowedBlocks={[
                                    'core/paragraph',
                                    'core/heading',
                                    'core/list',
                                    'core/quote',
                                    'core/image',
                                    'core/separator',
                                    'core/spacer',
                                    'core/buttons',
                                    'core/columns',
                                    'core/group'
                                ]}
                                template={TEMPLATE}
                                templateLock={false}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
