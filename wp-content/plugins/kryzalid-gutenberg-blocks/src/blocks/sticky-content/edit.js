/**
 * WordPress dependencies
 */
import { __ } from '@wordpress/i18n';
import {
    useBlockProps,
    InspectorControls,
    MediaUpload,
    MediaUploadCheck,
    RichText,
    InnerBlocks,
    BlockControls,
} from '@wordpress/block-editor';
import {
    PanelBody,
    Button,
    SelectControl,
    ToolbarGroup,
    ToolbarButton
} from '@wordpress/components';
import { Fragment } from '@wordpress/element';

/**
 * Edit component for the Sticky block
 */
export default function Edit({ attributes, setAttributes }) {
    const {
        imageId,
        imageUrl,
        imageAlt,
        title,
        imagePosition
    } = attributes;

    const blockProps = useBlockProps({
        className: `content-sticky-ctn ${imagePosition === 'right' ? '-image-right' : ''}`
    });

    const onSelectImage = (media) => {
        setAttributes({
            imageId: media.id,
            imageUrl: media.url,
            imageAlt: media.alt || ''
        });
    };

    const onRemoveImage = () => {
        setAttributes({
            imageId: 0,
            imageUrl: '',
            imageAlt: ''
        });
    };

    return (
        <Fragment>
            <InspectorControls>
                <PanelBody title={__('Paramètres du bloc', 'kryzalid-gutenberg-blocks')} initialOpen={true}>
                    <SelectControl
                        label={__('Position de l\'image', 'kryzalid-gutenberg-blocks')}
                        value={imagePosition}
                        options={[
                            { label: __('À gauche', 'kryzalid-gutenberg-blocks'), value: 'left' },
                            { label: __('À droite', 'kryzalid-gutenberg-blocks'), value: 'right' }
                        ]}
                        onChange={(value) => setAttributes({ imagePosition: value })}
                    />
                </PanelBody>
                
                <PanelBody title={__('Image', 'kryzalid-gutenberg-blocks')} initialOpen={false}>
                    <MediaUploadCheck>
                        <MediaUpload
                            onSelect={onSelectImage}
                            allowedTypes={['image']}
                            value={imageId}
                            render={({ open }) => (
                                <div>
                                    {imageUrl ? (
                                        <div>
                                            <img 
                                                src={imageUrl} 
                                                alt={imageAlt}
                                                style={{ width: '100%', height: 'auto', marginBottom: '10px' }}
                                            />
                                            <Button 
                                                onClick={onRemoveImage}
                                                isDestructive
                                                variant="secondary"
                                            >
                                                {__('Supprimer l\'image', 'kryzalid-gutenberg-blocks')}
                                            </Button>
                                        </div>
                                    ) : (
                                        <Button 
                                            onClick={open}
                                            variant="primary"
                                        >
                                            {__('Sélectionner une image', 'kryzalid-gutenberg-blocks')}
                                        </Button>
                                    )}
                                </div>
                            )}
                        />
                    </MediaUploadCheck>
                </PanelBody>
            </InspectorControls>

            <BlockControls>
                <ToolbarGroup>
                    <ToolbarButton
                        icon="align-pull-left"
                        label={__('Image à gauche', 'kryzalid-gutenberg-blocks')}
                        isActive={imagePosition === 'left'}
                        onClick={() => setAttributes({ imagePosition: 'left' })}
                    />
                    <ToolbarButton
                        icon="align-pull-right"
                        label={__('Image à droite', 'kryzalid-gutenberg-blocks')}
                        isActive={imagePosition === 'right'}
                        onClick={() => setAttributes({ imagePosition: 'right' })}
                    />
                </ToolbarGroup>
            </BlockControls>

            <div {...blockProps}>
                <div className="content-sticky-title">
                    <RichText
                        tagName="h2"
                        className=""
                        placeholder={__('Titre de la section...', 'kryzalid-gutenberg-blocks')}
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        allowedFormats={[]}
                    />
                </div>
                
                <div className="content-sticky-inner">
                    <div className="content-sticky-image">
                        <div className="image-sticky">
                            <div className="wp-block-image">
                                {imageUrl ? (
                                    <img src={imageUrl} alt={imageAlt} />
                                ) : (
                                    <div className="image-placeholder">
                                        <MediaUploadCheck>
                                            <MediaUpload
                                                onSelect={onSelectImage}
                                                allowedTypes={['image']}
                                                value={imageId}
                                                render={({ open }) => (
                                                    <Button 
                                                        onClick={open}
                                                        variant="primary"
                                                        className="image-placeholder-button"
                                                    >
                                                        {__('Ajouter une image', 'kryzalid-gutenberg-blocks')}
                                                    </Button>
                                                )}
                                            />
                                        </MediaUploadCheck>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    
                    <div className="content-sticky-content">
                        
                        <div className="content-sticky-intro">
                            <InnerBlocks
                                allowedBlocks={[
                                    'core/paragraph',
                                    'core/heading',
                                    'core/list',
                                    'core/quote',
                                    'core/image',
                                    'core/separator',
                                    'core/spacer',
                                    'core/buttons',
                                    'core/columns',
                                    'core/group'
                                ]}
                                template={[
                                    ['core/paragraph', { placeholder: 'Ajoutez votre contenu ici...' }]
                                ]}
                                templateLock={false}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Fragment>
    );
}
