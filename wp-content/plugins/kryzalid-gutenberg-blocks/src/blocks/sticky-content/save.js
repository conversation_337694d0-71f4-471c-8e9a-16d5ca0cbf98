/**
 * WordPress dependencies
 */
import { useBlockProps, RichText, InnerBlocks } from '@wordpress/block-editor';

/**
 * Save component for the Approche block
 */
export default function Save({ attributes }) {
    const {
        imageUrl,
        imageAlt,
        title,
        imagePosition,
    } = attributes;

    const blockProps = useBlockProps.save({
        className: `content-sticky-ctn ${imagePosition === 'right' ? '-image-right' : ''}`,
        'data-module-sticky-content': '',
    });

    return (
        <div {...blockProps}>
            {title && (
                <div className="content-sticky-title">
                    <RichText.Content
                        tagName="h2"
                        className="ctn"
                        value={title}
                    />
                </div>
            )}
            <div className="content-sticky-inner">
                {imageUrl && (
                    <div className="content-sticky-image">
                        <div className="image-sticky">
                            <div className="wp-block-image">
                                <img src={imageUrl} alt={imageAlt} />
                            </div>
                        </div>
                    </div>
                )}
                
                <div className="content-sticky-content">

                    <div className="content-sticky-intro">
                        <InnerBlocks.Content />
                    </div>
                </div>
            </div>
        </div>
    );
}
