import { __ } from '@wordpress/i18n';
import { useBlockProps, InspectorControls, InnerBlocks } from '@wordpress/block-editor';
import { PanelBody, ToggleControl, Button } from '@wordpress/components';
import { useSelect, useDispatch } from '@wordpress/data';
import { createBlock } from '@wordpress/blocks';

export default function Edit({ attributes, setAttributes, clientId }) {
    const { allowMultiple } = attributes;
    const blockProps = useBlockProps();

    const TEMPLATE = [
        ['kryzalid/accordion-item', { title: 'Titre de l\'accordéon' }]
    ];

    const ALLOWED_BLOCKS = ['kryzalid/accordion-item'];

    const { getBlocks } = useSelect('core/block-editor');
    const { insertBlock } = useDispatch('core/block-editor');

    const addAccordionItem = () => {

        const newBlock = createBlock('kryzalid/accordion-item', {
            title: 'Nouveau titre d\'accordéon',
            
        });

        console.log(newBlock);
        insertBlock(newBlock, undefined, clientId);
    };
    
    const hasInnerBlocks = getBlocks(clientId).length > 0;

    return (
        <>
            <InspectorControls>
                <PanelBody title={__('Paramètres de l\'accordéon', 'kryzalid-gutenberg-blocks')}>
                    <ToggleControl
                        label={__('Permettre plusieurs sections ouvertes', 'kryzalid-gutenberg-blocks')}
                        checked={allowMultiple}
                        onChange={(value) => setAttributes({ allowMultiple: value })}
                    />
                    <Button
                        variant="primary"
                        onClick={addAccordionItem}
                        style={{ marginTop: '10px' }}
                    >
                        {__('Ajouter une section d\'accordéon', 'kryzalid-gutenberg-blocks')}
                    </Button>
                </PanelBody>
            </InspectorControls>

            <div {...blockProps}>
                <div
                    className="kryzalid-accordion content-accordion-ctn"
                    data-allow-multiple={allowMultiple ? 'true' : 'false'}
                >
                    <InnerBlocks
                        allowedBlocks={ALLOWED_BLOCKS}
                        template={TEMPLATE}
                        templateLock={false}
                    />

                    {!hasInnerBlocks && (
                        <Button
                            variant="primary"
                            onClick={addAccordionItem}
                            style={{ marginTop: '10px' }}
                        >
                            {__('Ajouter une section d\'accordéon', 'kryzalid-gutenberg-blocks')}
                        </Button>
                    )}
                </div>
            </div>
        </>
    );
}