import { useBlockProps, InnerBlocks } from '@wordpress/block-editor';

export default function Save({ attributes }) {
    const { allowMultiple } = attributes;
    const blockProps = useBlockProps.save();

    return (
        <div {...blockProps}>
            <div
                data-module-accordion=""
                className="kryzalid-accordion content-accordion-ctn"
                data-allow-multiple={allowMultiple ? 'true' : 'false'}
            >
                <InnerBlocks.Content />
            </div>
        </div>
    );
}
