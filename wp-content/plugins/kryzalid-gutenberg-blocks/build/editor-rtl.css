/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/editor.scss ***!
  \****************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/* Styles pour l'éditeur des blocs Kryzalid */
/* Accordéon - Styles éditeur */
.kryzalid-accordion {
  padding: 16px;
}
.kryzalid-accordion .accordion-item {
  border: 1px solid var(--border-default, #ccc);
  border-radius: 4px;
  margin-bottom: 12px;
}
.kryzalid-accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.kryzalid-accordion .accordion-item .accordion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
}
.kryzalid-accordion .accordion-item .accordion-header .accordion-toggle {
  min-width: 32px;
  height: 32px;
  font-size: 24px;
  color: var(--title-color, black);
  border: none;
  cursor: pointer;
}
.kryzalid-accordion .accordion-item .accordion-header .accordion-title {
  flex: 1;
  padding: 16px 0;
  margin: 0;
}
.kryzalid-accordion .accordion-item .accordion-content {
  padding: 32px;
}
