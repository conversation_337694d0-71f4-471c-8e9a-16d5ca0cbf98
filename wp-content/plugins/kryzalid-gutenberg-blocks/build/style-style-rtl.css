/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/style.scss ***!
  \***************************************************************************************************************************************************************************************************************************************/
/**
 * Frontend styles for Kryzalid Gutenberg Blocks
 */
/* Styles frontend pour le bloc Sticky-content */
.wp-block-kryzalid-sticky,
.content-sticky-ctn {
  position: relative;
}
.wp-block-kryzalid-sticky .content-sticky-inner,
.content-sticky-ctn .content-sticky-inner {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--grid-gutter, 80px);
  align-items: start;
}
.wp-block-kryzalid-sticky .content-sticky-image,
.content-sticky-ctn .content-sticky-image {
  height: 100%;
}
.wp-block-kryzalid-sticky .content-sticky-image .image-sticky img,
.content-sticky-ctn .content-sticky-image .image-sticky img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  aspect-ratio: 1/1;
}
.wp-block-kryzalid-sticky.-image-right .content-sticky-inner .content-sticky-image,
.content-sticky-ctn.-image-right .content-sticky-inner .content-sticky-image {
  order: 2;
}
.wp-block-kryzalid-sticky.-image-right .content-sticky-inner .content-sticky-content,
.content-sticky-ctn.-image-right .content-sticky-inner .content-sticky-content {
  order: 1;
}

/* Responsive */
