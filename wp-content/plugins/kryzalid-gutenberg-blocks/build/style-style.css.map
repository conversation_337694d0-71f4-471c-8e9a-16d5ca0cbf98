{"version": 3, "file": "./style-style.css", "mappings": ";;;AAAA;;EAAA;AAIA;AACA;;EAEI;AAAJ;AAEI;;EACI;EACA;EACA;EACA;AACR;AAEI;;EACI;AACR;AACY;;EACI;EACA;KAAA;EACA;AAEhB;AAMY;;EACI;AAHhB;AAMY;;EACI;AAHhB;;AASA,gB", "sources": ["webpack://kry<PERSON><PERSON>-gut<PERSON>-blocks/./src/style.scss"], "sourcesContent": ["/**\n * Frontend styles for Kryzalid Gutenberg Blocks\n */\n\n/* Styles frontend pour le bloc Sticky-content */\n.wp-block-kryzalid-sticky,\n.content-sticky-ctn {\n    position: relative;\n\n    .content-sticky-inner {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: var(--grid-gutter, 80px);\n        align-items: start;\n    }\n\n    .content-sticky-image {\n        height: 100%;\n        .image-sticky {\n            img {\n                width: 100%;\n                object-fit: cover;\n                aspect-ratio: 1/1;\n            }\n        }\n    }\n    \n    // Variante image à droite\n    &.-image-right {\n        .content-sticky-inner {\n            .content-sticky-image {\n                order: 2;\n            }\n            \n            .content-sticky-content {\n                order: 1;\n            }\n        }\n    }\n}\n\n/* Responsive */"], "names": [], "sourceRoot": ""}