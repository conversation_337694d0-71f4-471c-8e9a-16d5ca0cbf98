{"version": 3, "file": "editor.css", "mappings": ";;;AAAA,gBAAgB;AAAhB;AAEA;AACA;EACI;AACJ;AACI;EACI;EACA;EACA;AACR;AACQ;EACI;AACZ;AAEQ;EACI;EACA;EACA;EACA;AAAZ;AAEY;EACI;EACA;EACA;EACA;EACA;EACA;AAAhB;AAGY;EACI;EACA;EACA;AADhB;AAKQ;EACI;AAHZ,C", "sources": ["webpack://kry<PERSON><PERSON>-gut<PERSON>-blocks/./src/editor.scss"], "sourcesContent": ["/* Styles pour l'éditeur des blocs Kryzalid */\n\n/* Accord<PERSON><PERSON> - Styles éditeur */\n.kryzalid-accordion {\n    padding: 16px;\n\n    .accordion-item {\n        border: 1px solid var(--border-default, #ccc);\n        border-radius: 4px;\n        margin-bottom: 12px;\n\n        &:last-child {\n            margin-bottom: 0;\n        }\n\n        .accordion-header {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            padding: 16px 32px;\n\n            .accordion-toggle {\n                min-width: 32px;\n                height: 32px;\n                font-size: 24px;\n                color: var(--title-color, black);\n                border: none;\n                cursor: pointer;\n            }\n\n            .accordion-title {\n                flex: 1;\n                padding: 16px 0;\n                margin: 0;\n            }\n        }\n\n        .accordion-content {\n            padding: 32px;\n        }\n    }\n}"], "names": [], "sourceRoot": ""}