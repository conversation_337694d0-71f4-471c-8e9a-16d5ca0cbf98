{"version": 3, "file": "editor.js", "mappings": ";;UAAA;UACA;;;;;WCDA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;ACNA", "sources": ["webpack://kry<PERSON><PERSON>-gutenberg-blocks/webpack/bootstrap", "webpack://kry<PERSON><PERSON>-gut<PERSON>-blocks/webpack/runtime/make namespace object", "webpack://kry<PERSON><PERSON>-gut<PERSON>-blocks/./src/editor.scss?0339"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// extracted by mini-css-extract-plugin\nexport {};"], "names": [], "sourceRoot": ""}