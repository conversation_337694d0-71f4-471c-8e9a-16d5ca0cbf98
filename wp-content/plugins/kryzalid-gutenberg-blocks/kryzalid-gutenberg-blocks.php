<?php
/**
 * Plugin Name: <PERSON><PERSON><PERSON><PERSON>utenberg Blocks
 * Plugin URI: https://kryzalid.com
 * Description: Collection de blocs Gutenberg personnalisés développés par Kryzalid pour créer des interfaces modernes et interactives.
 * Version: 1.0.0
 * Author: Kryzalid
 * Author URI: https://kryzalid.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: kryzalid-gutenberg-blocks
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * Network: false
 *
 * @package KryzalidGutenbergBlocks
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('KGB_VERSION', '1.0.0');
define('KGB_PLUGIN_FILE', __FILE__);
define('KGB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('KGB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('KGB_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class KryzalidGutenbergBlocks {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_block_editor_assets'));
        add_action('enqueue_block_assets', array($this, 'enqueue_block_assets'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        
        // Plugin lifecycle hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Register blocks
        $this->register_blocks();
        
        // Add block categories
        add_filter('block_categories_all', array($this, 'add_block_categories'));
    }
    
    /**
     * Register all blocks
     */
    private function register_blocks() {
        // Register sticky-content block
        if (file_exists(KGB_PLUGIN_DIR . 'blocks/sticky-content/block.json')) {
            register_block_type(KGB_PLUGIN_DIR . 'blocks/sticky-content/block.json');
        }
        // Register accordion block
        if (file_exists(KGB_PLUGIN_DIR . 'blocks/accordion/block.json')) {
            register_block_type(KGB_PLUGIN_DIR . 'blocks/accordion/block.json');
        }
    }

    
    /**
     * Enqueue block editor assets
     */
    public function enqueue_block_editor_assets() {
        // Enqueue block scripts
        wp_enqueue_script(
            'kryzalid-blocks-editor',
            KGB_PLUGIN_URL . 'build/index.js',
            array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
            KGB_VERSION
        );

        // Enqueue editor styles
        wp_enqueue_style(
            'kryzalid-blocks-editor',
            KGB_PLUGIN_URL . 'build/editor.css',
            array(),
            KGB_VERSION
        );
    }
    
    /**
     * Enqueue block assets (frontend + editor)
     */
    public function enqueue_block_assets() {
        // Enqueue frontend styles
        wp_enqueue_style(
            'kryzalid-blocks-style',
            KGB_PLUGIN_URL . 'build/style-style.css',
            array(),
            KGB_VERSION
        );
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Enqueue frontend JavaScript
        wp_enqueue_script(
            'kryzalid-blocks-frontend',
            KGB_PLUGIN_URL . 'build/frontend.js',
            array(),
            KGB_VERSION,
            true
        );
    }
    
    /**
     * Add custom block categories
     */
    public function add_block_categories($categories) {
        return array_merge(
            $categories,
            array(
                array(
                    'slug'  => 'kryzalid-blocks',
                    'title' => __('Kryzalid Blocks', 'kryzalid-gutenberg-blocks'),
                    'icon'  => 'layout',
                ),
            )
        );
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'kryzalid-gutenberg-blocks',
            false,
            dirname(KGB_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initialize the plugin
KryzalidGutenbergBlocks::get_instance();
