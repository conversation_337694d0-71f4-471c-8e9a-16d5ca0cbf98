const defaultConfig = require('@wordpress/scripts/config/webpack.config');
const path = require('path');

module.exports = {
    ...defaultConfig,
    entry: {
        index: path.resolve(process.cwd(), 'src', 'index.js'),
        editor: path.resolve(process.cwd(), 'src', 'editor.scss'),
        style: path.resolve(process.cwd(), 'src', 'style.scss'),
    },
    output: {
        ...defaultConfig.output,
        filename: '[name].js',
    },
};
