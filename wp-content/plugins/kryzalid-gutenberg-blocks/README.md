# Kryzalid <PERSON>nberg Blocks

Collection de blocs Gutenberg personnalisés développés par Kryzalid pour créer des interfaces modernes et interactives.

## Description

Ce plugin WordPress fournit une collection de blocs Gutenberg personnalisés conçus pour améliorer l'expérience de création de contenu. Chaque bloc est développé avec les meilleures pratiques et offre une interface utilisateur intuitive.

## Blocs disponibles

### 🎯 Approche – Image sticky + contenu
- **Description** : Bloc avec image qui reste fixe pendant le défilement du contenu
- **Fonctionnalités** :
  - Image sticky responsive
  - Zone de contenu flexible avec InnerBlocks
  - Support de tous les blocs Gutenberg (paragraphes, titres, listes, citations, etc.)
  - Position configurable (gauche/droite)
  - Contrôles dans l'inspecteur et la barre d'outils
  - Animations d'apparition progressive

## Installation

### Méthode 1 : Installation manuelle
1. Télécharger le plugin
2. Décompresser dans `/wp-content/plugins/`
3. Activer le plugin dans l'administration WordPress

### Méthode 2 : Développement
1. C<PERSON>r le repository dans `/wp-content/plugins/`
2. Installer les dépendances : `npm install`
3. Compiler les assets : `npm run build`
4. Activer le plugin

## Développement

### Prérequis
- Node.js 18+
- npm ou yarn
- WordPress 6.0+
- PHP 8.0+

### Scripts disponibles
```bash
# Développement avec watch
npm run dev

# Build de production
npm run build

# Linting
npm run lint:js
npm run lint:css

# Formatage du code
npm run format
```

### Structure du projet
```
kryzalid-gutenberg-blocks/
├── blocks/                 # Métadonnées des blocs (block.json)
│   └── approche/
├── src/                    # Code source
│   ├── blocks/            # Composants React des blocs
│   ├── index.js           # Point d'entrée principal
│   ├── editor.scss        # Styles éditeur
│   ├── style.scss         # Styles frontend
│   └── frontend.js        # JavaScript frontend
├── dist/                  # Assets compilés
├── languages/             # Fichiers de traduction
└── kryzalid-gutenberg-blocks.php  # Fichier principal du plugin
```

## Utilisation

### Bloc Approche
1. Activer le plugin
2. Dans l'éditeur Gutenberg, rechercher "Kryzalid Blocks"
3. Ajouter le bloc "Approche – Image sticky + contenu"
4. Configurer l'image et le titre via l'inspecteur
5. Ajouter du contenu dans la zone flexible :
   - **Paragraphes** : Texte avec formatage
   - **Titres** : H1 à H6 pour structurer le contenu
   - **Listes** : Listes à puces ou numérotées
   - **Citations** : Blocs de citation stylisés
   - **Images** : Images supplémentaires dans le contenu
   - **Séparateurs** : Lignes de séparation
   - **Boutons** : Appels à l'action
   - **Colonnes** : Mise en page en colonnes
   - **Groupes** : Regroupement d'éléments

## Fonctionnalités techniques

- **Blocs natifs Gutenberg** : Utilise l'API officielle de WordPress
- **InnerBlocks** : Zone de contenu flexible acceptant tous les blocs Gutenberg
- **Rendu côté serveur** : Optimisé pour le SEO
- **Responsive design** : Adaptation automatique sur tous les appareils
- **Performance** : Code optimisé et assets minifiés
- **Accessibilité** : Respect des standards WCAG
- **Internationalization** : Support multilingue

## Compatibilité

- WordPress 6.0+
- PHP 8.0+
- Gutenberg (inclus dans WordPress)
- Thèmes compatibles avec les blocs Gutenberg

## Support

Pour le support technique ou les demandes de fonctionnalités :
- Email : <EMAIL>
- Site web : https://kryzalid.com

## Licence

GPL v2 or later

## Changelog

### 1.0.0
- Version initiale
- Bloc Approche avec image sticky
- Interface d'administration complète
- Documentation complète

## Crédits

Développé par [Kryzalid](https://kryzalid.com) - Agence digitale spécialisée dans le développement WordPress.
